<?php
session_start();
header('Content-Type: text/html; charset=utf-8');

echo "<h2>Localhost vs Online Environment Comparison</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.success { color: #28a745; background: #d4edda; padding: 8px; border-radius: 4px; margin: 5px 0; }
.error { color: #dc3545; background: #f8d7da; padding: 8px; border-radius: 4px; margin: 5px 0; }
.warning { color: #856404; background: #fff3cd; padding: 8px; border-radius: 4px; margin: 5px 0; }
.info { color: #0c5460; background: #d1ecf1; padding: 8px; border-radius: 4px; margin: 5px 0; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
.section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
.comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
.localhost, .online { padding: 15px; border-radius: 8px; }
.localhost { background: #e8f5e8; border: 2px solid #28a745; }
.online { background: #fff3cd; border: 2px solid #ffc107; }
pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
</style>";

echo "<div class='container'>";

// Environment Detection
$is_localhost = (
    $_SERVER['HTTP_HOST'] === 'localhost' || 
    $_SERVER['HTTP_HOST'] === '127.0.0.1' || 
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0 ||
    strpos($_SERVER['HTTP_HOST'], '127.0.0.1:') === 0
);

echo "<div class='info'>";
echo "<strong>Current Environment:</strong> " . ($is_localhost ? "🏠 Localhost" : "🌐 Online Server");
echo "<br><strong>Host:</strong> " . $_SERVER['HTTP_HOST'];
echo "<br><strong>Server IP:</strong> " . ($_SERVER['SERVER_ADDR'] ?? 'Unknown');
echo "</div>";

// 1. PHP Environment Comparison
echo "<div class='section'>";
echo "<h3>1. PHP Environment</h3>";
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$php_settings = [
    'PHP Version' => phpversion(),
    'Error Reporting' => error_reporting(),
    'Display Errors' => ini_get('display_errors') ? 'On' : 'Off',
    'Log Errors' => ini_get('log_errors') ? 'On' : 'Off',
    'Max Execution Time' => ini_get('max_execution_time') . 's',
    'Memory Limit' => ini_get('memory_limit'),
    'Upload Max Filesize' => ini_get('upload_max_filesize'),
    'Post Max Size' => ini_get('post_max_size'),
    'Session Save Path' => session_save_path(),
    'Session Name' => session_name(),
    'Default Charset' => ini_get('default_charset')
];

foreach ($php_settings as $setting => $value) {
    $status = 'info';
    $note = '';
    
    if ($setting === 'Display Errors' && $value === 'On' && !$is_localhost) {
        $status = 'warning';
        $note = 'Should be Off in production';
    } elseif ($setting === 'PHP Version' && version_compare($value, '7.4', '<')) {
        $status = 'warning';
        $note = 'Consider upgrading';
    }
    
    echo "<tr>";
    echo "<td>$setting</td>";
    echo "<td>$value</td>";
    echo "<td><span class='$status'>$note</span></td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 2. Database Connection Test
echo "<div class='section'>";
echo "<h3>2. Database Connection</h3>";

if (file_exists('../db/dbconfig.php')) {
    echo "<div class='success'>✅ Database config file exists</div>";
    
    try {
        include '../db/dbconfig.php';
        
        if ($connection) {
            echo "<div class='success'>✅ Database connected successfully</div>";
            
            // Get database info
            $db_info = [
                'Server Version' => mysqli_get_server_info($connection),
                'Client Version' => mysqli_get_client_info(),
                'Host Info' => mysqli_get_host_info($connection),
                'Protocol Version' => mysqli_get_proto_info($connection),
                'Character Set' => mysqli_character_set_name($connection),
                'Database Name' => mysqli_select_db($connection, '') ? 'Connected' : 'Unknown'
            ];
            
            echo "<table>";
            echo "<tr><th>Database Setting</th><th>Value</th></tr>";
            foreach ($db_info as $key => $value) {
                echo "<tr><td>$key</td><td>$value</td></tr>";
            }
            echo "</table>";
            
            // Test table existence
            echo "<h4>Table Existence Check:</h4>";
            $tables = ['tbl_users', 'tbl_chat', 'tbl_chat_files', 'tbl_conversations'];
            foreach ($tables as $table) {
                $result = mysqli_query($connection, "SHOW TABLES LIKE '$table'");
                if (mysqli_num_rows($result) > 0) {
                    echo "<div class='success'>✅ $table exists</div>";
                    
                    // Get row count
                    $count_result = mysqli_query($connection, "SELECT COUNT(*) as count FROM $table");
                    if ($count_result) {
                        $count = mysqli_fetch_assoc($count_result)['count'];
                        echo "<div class='info'>   → $count rows</div>";
                    }
                } else {
                    echo "<div class='error'>❌ $table missing</div>";
                }
            }
            
        } else {
            echo "<div class='error'>❌ Database connection failed: " . mysqli_connect_error() . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='error'>❌ Database config file not found</div>";
}
echo "</div>";

// 3. Session Analysis
echo "<div class='section'>";
echo "<h3>3. Session Analysis</h3>";

echo "<table>";
echo "<tr><th>Session Info</th><th>Value</th></tr>";
echo "<tr><td>Session ID</td><td>" . session_id() . "</td></tr>";
echo "<tr><td>Session Status</td><td>" . session_status() . "</td></tr>";
echo "<tr><td>Session Save Path</td><td>" . session_save_path() . "</td></tr>";
echo "<tr><td>Session Cookie Path</td><td>" . session_get_cookie_params()['path'] . "</td></tr>";
echo "<tr><td>Session Cookie Domain</td><td>" . session_get_cookie_params()['domain'] . "</td></tr>";
echo "</table>";

if (!empty($_SESSION)) {
    echo "<h4>Session Variables:</h4>";
    echo "<table>";
    echo "<tr><th>Key</th><th>Value</th><th>Type</th></tr>";
    foreach ($_SESSION as $key => $value) {
        echo "<tr>";
        echo "<td>$key</td>";
        echo "<td>" . (is_array($value) ? json_encode($value) : $value) . "</td>";
        echo "<td>" . gettype($value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for user ID
    $user_id_found = false;
    $user_id_value = null;
    $user_id_key = null;
    
    foreach (['id', 'user_id', 'userid', 'ID', 'USER_ID'] as $key) {
        if (isset($_SESSION[$key])) {
            $user_id_found = true;
            $user_id_value = $_SESSION[$key];
            $user_id_key = $key;
            break;
        }
    }
    
    if ($user_id_found) {
        echo "<div class='success'>✅ User ID found: $user_id_value (key: $user_id_key)</div>";
    } else {
        echo "<div class='error'>❌ No user ID found in session</div>";
    }
} else {
    echo "<div class='warning'>⚠️ No session data found - user not logged in</div>";
}
echo "</div>";

// 4. File System Check
echo "<div class='section'>";
echo "<h3>4. File System Check</h3>";

$files_to_check = [
    'chat_api.php' => 'Main chat API',
    'chat_realtime.php' => 'Real-time polling API',
    'chatbox.js' => 'Chat JavaScript',
    'emoticons.js' => 'Emoji picker JavaScript',
    '../db/dbconfig.php' => 'Database configuration'
];

echo "<table>";
echo "<tr><th>File</th><th>Exists</th><th>Readable</th><th>Size</th><th>Modified</th></tr>";

foreach ($files_to_check as $file => $description) {
    $exists = file_exists($file);
    $readable = $exists ? is_readable($file) : false;
    $size = $exists ? filesize($file) : 0;
    $modified = $exists ? date('Y-m-d H:i:s', filemtime($file)) : 'N/A';
    
    echo "<tr>";
    echo "<td>$file<br><small>$description</small></td>";
    echo "<td>" . ($exists ? '✅' : '❌') . "</td>";
    echo "<td>" . ($readable ? '✅' : '❌') . "</td>";
    echo "<td>" . ($exists ? number_format($size) . ' bytes' : 'N/A') . "</td>";
    echo "<td>$modified</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 5. Server Environment
echo "<div class='section'>";
echo "<h3>5. Server Environment</h3>";

echo "<table>";
echo "<tr><th>Server Variable</th><th>Value</th></tr>";

$server_vars = [
    'SERVER_SOFTWARE' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'Unknown',
    'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
    'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
    'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'SCRIPT_FILENAME' => $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown',
    'HTTPS' => isset($_SERVER['HTTPS']) ? 'Yes' : 'No',
    'HTTP_USER_AGENT' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown', 0, 100) . '...'
];

foreach ($server_vars as $var => $value) {
    echo "<tr><td>$var</td><td>$value</td></tr>";
}
echo "</table>";
echo "</div>";

// 6. Common Online Hosting Issues
echo "<div class='section'>";
echo "<h3>6. Common Online Hosting Issues & Solutions</h3>";

echo "<div class='warning'>";
echo "<h4>🔧 Most Common Issues:</h4>";
echo "<ol>";
echo "<li><strong>Different Session Variables:</strong> Online might use different session key names</li>";
echo "<li><strong>Missing Database Tables:</strong> Tables exist on localhost but not online</li>";
echo "<li><strong>File Permissions:</strong> Online server has stricter permissions</li>";
echo "<li><strong>PHP Configuration:</strong> Different PHP settings between environments</li>";
echo "<li><strong>Database Charset:</strong> Online database might not support UTF-8MB4</li>";
echo "<li><strong>Error Reporting:</strong> Errors hidden in production but visible in localhost</li>";
echo "</ol>";
echo "</div>";

echo "<div class='info'>";
echo "<h4>🚀 Quick Fixes to Try:</h4>";
echo "<ol>";
echo "<li><strong>Export/Import Database:</strong> Export your localhost database and import to online</li>";
echo "<li><strong>Check Session Keys:</strong> Verify which session variable contains user ID online</li>";
echo "<li><strong>Run SQL Script:</strong> Execute create_chat_tables.sql on online database</li>";
echo "<li><strong>Enable Error Logging:</strong> Check server error logs for specific errors</li>";
echo "<li><strong>Test API Directly:</strong> Visit chat_api.php?action=test in browser</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

// 7. Direct API Test
echo "<div class='section'>";
echo "<h3>7. Direct API Test</h3>";
echo "<p>Click the button below to test the API directly:</p>";
echo "<button onclick='testAPI()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;'>Test Chat API</button>";
echo "<div id='api-result' style='margin-top: 10px;'></div>";
echo "</div>";

echo "</div>";

// JavaScript for API testing
echo "<script>
async function testAPI() {
    const resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = '<div style=\"color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px;\">Testing API...</div>';
    
    try {
        const response = await fetch('chat_api.php?action=get_conversations');
        const text = await response.text();
        
        let resultHTML = '<h4>API Response:</h4>';
        resultHTML += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        resultHTML += '<p><strong>Content-Type:</strong> ' + response.headers.get('content-type') + '</p>';
        resultHTML += '<p><strong>Raw Response:</strong></p>';
        resultHTML += '<pre>' + text + '</pre>';
        
        // Try to parse as JSON
        try {
            const json = JSON.parse(text);
            resultHTML += '<div style=\"color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0;\">✅ Valid JSON Response</div>';
            resultHTML += '<p><strong>Parsed JSON:</strong></p>';
            resultHTML += '<pre>' + JSON.stringify(json, null, 2) + '</pre>';
        } catch (e) {
            resultHTML += '<div style=\"color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0;\">❌ Invalid JSON: ' + e.message + '</div>';
            
            // Analyze the response
            if (text.includes('Fatal error')) {
                resultHTML += '<div style=\"color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px;\">🔧 <strong>Fix:</strong> PHP Fatal Error detected. Check error logs.</div>';
            } else if (text.includes('Unauthorized')) {
                resultHTML += '<div style=\"color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px;\">🔧 <strong>Fix:</strong> User not logged in. Log in first.</div>';
            } else if (text.includes('Unknown table')) {
                resultHTML += '<div style=\"color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px;\">🔧 <strong>Fix:</strong> Database tables missing. Run create_chat_tables.sql</div>';
            } else if (text.includes('<html') || text.includes('<!DOCTYPE')) {
                resultHTML += '<div style=\"color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px;\">🔧 <strong>Fix:</strong> Server returning HTML error page instead of JSON.</div>';
            }
        }
        
        resultDiv.innerHTML = resultHTML;
        
    } catch (error) {
        resultDiv.innerHTML = '<div style=\"color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px;\">❌ Network Error: ' + error.message + '</div>';
    }
}
</script>";
?>
