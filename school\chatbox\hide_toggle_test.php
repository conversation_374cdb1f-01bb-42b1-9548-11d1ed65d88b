<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Hide Chat Toggle Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .info-box {
            background: #6f42c1;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
            max-width: 200px;
        }
        
        .test-buttons {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
        }
        
        .test-buttons button {
            margin: 5px 0;
            display: block;
            width: 150px;
        }
        
        .content-area {
            height: 120vh;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 8px;
        }
        
        .toggle-indicator {
            position: fixed;
            bottom: 140px;
            right: 30px;
            background: rgba(255, 193, 7, 0.8);
            color: black;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10001;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="status-info" id="status-info">
        <div><strong>👁️ Toggle Status</strong></div>
        <div>Chat: <span id="chat-status">Closed</span></div>
        <div>Toggle: <span id="toggle-visibility">Visible</span></div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div>Test: <span id="test-result">Ready</span></div>
        </div>
    </div>
    
    <div class="test-buttons">
        <button class="btn btn-primary btn-sm" onclick="openChat()">
            <i class="fa fa-comments"></i> Open Chat
        </button>
        <button class="btn btn-secondary btn-sm" onclick="closeChat()">
            <i class="fa fa-times"></i> Close Chat
        </button>
        <button class="btn btn-info btn-sm" onclick="checkStatus()">
            <i class="fa fa-search"></i> Check Status
        </button>
    </div>
    
    <div class="toggle-indicator" id="toggle-indicator">
        ← Chat Toggle Should Be Here
    </div>
    
    <div class="info-box">
        <h2><i class="fa fa-eye-slash"></i> Hide Chat Toggle Test</h2>
        <h4>Auto-Hide When Chat Opens</h4>
        <p><strong>Goal:</strong> Chat toggle disappears when chat is open</p>
        <p><strong>Benefit:</strong> Clean interface, no button conflicts</p>
    </div>
    
    <div class="content-area">
        <h2>Hide Toggle Functionality</h2>
        
        <div class="alert alert-info">
            <h5><i class="fa fa-info-circle"></i> How It Works</h5>
            <p>The chat toggle button automatically hides when the chat is opened and reappears when closed:</p>
            <ul>
                <li><strong>Chat Closed:</strong> Toggle button visible (for opening chat)</li>
                <li><strong>Chat Open:</strong> Toggle button hidden (not needed)</li>
                <li><strong>Chat Closed Again:</strong> Toggle button reappears</li>
            </ul>
        </div>
        
        <h3>Test Scenarios:</h3>
        <div class="row">
            <div class="col-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">✅ Scenario 1: Open Chat</h6>
                        <p class="card-text">Click "Open Chat" button → Chat toggle should disappear</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">✅ Scenario 2: Close Chat</h6>
                        <p class="card-text">Click "Close Chat" or X button → Chat toggle should reappear</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">✅ Scenario 3: Toggle Chat</h6>
                        <p class="card-text">Click chat toggle itself → Should hide after opening chat</p>
                    </div>
                </div>
            </div>
        </div>
        
        <h3>Implementation Details:</h3>
        <div class="alert alert-success">
            <h6><i class="fa fa-code"></i> JavaScript Solution</h6>
            <p>Added to <code>openChatPanel()</code> and <code>closeChatPanel()</code> functions:</p>
            <pre style="background: rgba(0,0,0,0.1); padding: 10px; border-radius: 4px; font-size: 12px;">
// Hide when opening
document.getElementById('chat-toggle').style.display = 'none';

// Show when closing  
document.getElementById('chat-toggle').style.display = 'block';
            </pre>
        </div>
        
        <div class="alert alert-warning">
            <h6><i class="fa fa-css3-alt"></i> CSS Backup</h6>
            <p>Also added CSS rule as backup:</p>
            <pre style="background: rgba(0,0,0,0.1); padding: 10px; border-radius: 4px; font-size: 12px;">
.chat-panel.open~.chat-toggle {
    display: none;
}
            </pre>
        </div>
        
        <h3>Benefits:</h3>
        <ul>
            <li><strong>Clean Interface:</strong> No unnecessary buttons when chat is open</li>
            <li><strong>No Conflicts:</strong> Eliminates overlap with send button</li>
            <li><strong>Better UX:</strong> Intuitive behavior - toggle only when needed</li>
            <li><strong>Mobile Optimized:</strong> Saves screen space on small devices</li>
        </ul>
        
        <!-- Extra content for scrolling -->
        <div style="height: 200px; background: white; margin: 20px 0; padding: 20px; border-radius: 8px;">
            <h4>Sample Content</h4>
            <p>This content ensures the page is scrollable and tests the fixed positioning behavior of the chat toggle button.</p>
            <p>The hide/show functionality should work regardless of page scroll position.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        function updateStatus() {
            const chatPanel = document.getElementById('chat-panel');
            const chatToggle = document.getElementById('chat-toggle');
            
            if (chatPanel && chatToggle) {
                const isOpen = chatPanel.classList.contains('open');
                const isVisible = window.getComputedStyle(chatToggle).display !== 'none';
                
                document.getElementById('chat-status').textContent = isOpen ? 'Open' : 'Closed';
                document.getElementById('toggle-visibility').textContent = isVisible ? 'Visible' : 'Hidden';
                
                // Update test result
                const testElement = document.getElementById('test-result');
                if (isOpen && !isVisible) {
                    testElement.textContent = '✅ Perfect';
                    testElement.style.color = '#28a745';
                } else if (!isOpen && isVisible) {
                    testElement.textContent = '✅ Good';
                    testElement.style.color = '#28a745';
                } else if (isOpen && isVisible) {
                    testElement.textContent = '❌ Should Hide';
                    testElement.style.color = '#dc3545';
                } else {
                    testElement.textContent = '❌ Should Show';
                    testElement.style.color = '#dc3545';
                }
                
                // Update indicator
                const indicator = document.getElementById('toggle-indicator');
                if (isVisible) {
                    indicator.style.display = 'block';
                    indicator.textContent = '← Chat Toggle Here';
                    indicator.style.background = 'rgba(255, 193, 7, 0.8)';
                } else {
                    indicator.style.display = 'block';
                    indicator.textContent = '← Toggle Hidden ✅';
                    indicator.style.background = 'rgba(40, 167, 69, 0.8)';
                    indicator.style.color = 'white';
                }
            }
        }
        
        function openChat() {
            console.log('🔧 Opening chat...');
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                setTimeout(updateStatus, 500);
            }
        }
        
        function closeChat() {
            console.log('🔧 Closing chat...');
            const closeButton = document.getElementById('close-chat');
            if (closeButton) {
                closeButton.click();
                setTimeout(updateStatus, 500);
            }
        }
        
        function checkStatus() {
            updateStatus();
            const chatPanel = document.getElementById('chat-panel');
            const chatToggle = document.getElementById('chat-toggle');
            
            if (chatPanel && chatToggle) {
                const isOpen = chatPanel.classList.contains('open');
                const isVisible = window.getComputedStyle(chatToggle).display !== 'none';
                
                alert(`📊 Current Status:
                
Chat Panel: ${isOpen ? 'Open' : 'Closed'}
Toggle Visible: ${isVisible ? 'Yes' : 'No'}
Expected: ${isOpen ? 'Toggle should be hidden' : 'Toggle should be visible'}
Result: ${(isOpen && !isVisible) || (!isOpen && isVisible) ? '✅ Correct' : '❌ Issue detected'}`);
            }
        }
        
        // Auto-update status
        setInterval(updateStatus, 1000);
        
        // Initial status check
        setTimeout(updateStatus, 1000);
        
        console.log('👁️ Hide toggle test loaded');
        console.log('Expected: Toggle hides when chat opens, shows when chat closes');
    </script>
</body>
</html>
