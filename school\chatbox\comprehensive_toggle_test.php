<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Comprehensive Toggle Hide Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .info-box {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.95);
            color: white;
            padding: 20px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            border: 2px solid #ffc107;
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #007bff;
        }
        
        .test-controls button {
            margin: 3px 0;
            display: block;
            width: 180px;
            font-size: 12px;
        }
        
        .content-area {
            height: 120vh;
            background: linear-gradient(to bottom, #ffe6e6, #ffcccc);
            padding: 20px;
            border-radius: 8px;
        }
        
        .force-hide-btn {
            position: fixed;
            bottom: 200px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
        }
    </style>
</head>
<body>
    <div class="status-panel" id="status-panel">
        <div><strong>🔍 Real-Time Status</strong></div>
        <div>Chat Panel: <span id="panel-status">Checking...</span></div>
        <div>Chat Window: <span id="window-status">Checking...</span></div>
        <div>Toggle Display: <span id="toggle-display">Checking...</span></div>
        <div>Toggle Computed: <span id="toggle-computed">Checking...</span></div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div>Expected: <span id="expected-state">Checking...</span></div>
            <div>Result: <span id="test-result">Checking...</span></div>
        </div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div style="font-size: 10px;">Auto-refresh every 500ms</div>
        </div>
    </div>
    
    <div class="test-controls">
        <div><strong>🧪 Test Controls</strong></div>
        <button class="btn btn-primary btn-sm" onclick="clickToggle()">
            <i class="fa fa-comments"></i> Click Toggle
        </button>
        <button class="btn btn-success btn-sm" onclick="openPanel()">
            <i class="fa fa-window-maximize"></i> Open Panel
        </button>
        <button class="btn btn-warning btn-sm" onclick="openWindow()">
            <i class="fa fa-external-link-alt"></i> Open Window
        </button>
        <button class="btn btn-secondary btn-sm" onclick="closeAll()">
            <i class="fa fa-times"></i> Close All
        </button>
        <button class="btn btn-info btn-sm" onclick="forceCheck()">
            <i class="fa fa-sync"></i> Force Check
        </button>
        <button class="btn btn-danger btn-sm" onclick="debugInfo()">
            <i class="fa fa-bug"></i> Debug Info
        </button>
    </div>
    
    <button class="btn btn-warning btn-lg force-hide-btn" onclick="forceHideToggle()">
        <i class="fa fa-eye-slash"></i> Force Hide Toggle
    </button>
    
    <div class="info-box">
        <h2><i class="fa fa-bug"></i> Comprehensive Toggle Hide Test</h2>
        <h4>Multiple Solutions Applied</h4>
        <p><strong>Problem:</strong> Chat toggle still showing when chat is open</p>
        <p><strong>Solutions:</strong> CSS + JavaScript + Monitor + Force Hide</p>
    </div>
    
    <div class="content-area">
        <h2>Multiple Hide Solutions</h2>
        
        <div class="alert alert-danger">
            <h5><i class="fa fa-exclamation-triangle"></i> The Problem</h5>
            <p>Chat toggle button remains visible even when chatbox is open, causing potential conflicts.</p>
        </div>
        
        <div class="alert alert-success">
            <h5><i class="fa fa-tools"></i> Applied Solutions</h5>
            <ol>
                <li><strong>CSS Rule:</strong> <code>.chat-panel.open~.chat-toggle { display: none !important; }</code></li>
                <li><strong>JavaScript Hide:</strong> <code>chatToggle.style.display = 'none'</code> in open functions</li>
                <li><strong>MutationObserver:</strong> Monitors chat state changes automatically</li>
                <li><strong>Periodic Check:</strong> Backup check every 1000ms</li>
                <li><strong>Force Hide:</strong> Manual override button for testing</li>
            </ol>
        </div>
        
        <h3>Test All Scenarios:</h3>
        <div class="row">
            <div class="col-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🔵 Test 1: Click Toggle Button</h6>
                        <p class="card-text">Click "Click Toggle" → Should open chat and hide toggle</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🟢 Test 2: Open Panel Directly</h6>
                        <p class="card-text">Click "Open Panel" → Should hide toggle</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🟡 Test 3: Open Window</h6>
                        <p class="card-text">Click "Open Window" → Should hide toggle</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">⚫ Test 4: Close All</h6>
                        <p class="card-text">Click "Close All" → Should show toggle</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🔴 Test 5: Force Hide</h6>
                        <p class="card-text">Click "Force Hide Toggle" → Should hide toggle regardless</p>
                    </div>
                </div>
            </div>
        </div>
        
        <h3>Real-Time Monitoring:</h3>
        <p>The status panel (top-right) shows real-time information about:</p>
        <ul>
            <li><strong>Chat Panel:</strong> Whether chat panel has 'open' class</li>
            <li><strong>Chat Window:</strong> Whether chat window has 'open' class</li>
            <li><strong>Toggle Display:</strong> Direct style.display value</li>
            <li><strong>Toggle Computed:</strong> Computed CSS display value</li>
            <li><strong>Expected:</strong> What the toggle visibility should be</li>
            <li><strong>Result:</strong> Whether it matches expectation</li>
        </ul>
        
        <div class="alert alert-info">
            <h6><i class="fa fa-lightbulb"></i> How to Use</h6>
            <p>Watch the status panel while testing different scenarios. The "Result" should always show "✅ Correct" when the toggle visibility matches the expected state.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        function updateStatus() {
            const chatPanel = document.getElementById('chat-panel');
            const chatWindow = document.getElementById('chat-window');
            const chatToggle = document.getElementById('chat-toggle');
            
            if (chatPanel && chatToggle) {
                const isPanelOpen = chatPanel.classList.contains('open');
                const isWindowOpen = chatWindow ? chatWindow.classList.contains('open') : false;
                const isChatOpen = isPanelOpen || isWindowOpen;
                
                const toggleStyle = chatToggle.style.display;
                const toggleComputed = window.getComputedStyle(chatToggle).display;
                const isToggleVisible = toggleComputed !== 'none';
                
                // Update status display
                document.getElementById('panel-status').textContent = isPanelOpen ? 'Open' : 'Closed';
                document.getElementById('window-status').textContent = isWindowOpen ? 'Open' : 'Closed';
                document.getElementById('toggle-display').textContent = toggleStyle || 'default';
                document.getElementById('toggle-computed').textContent = toggleComputed;
                
                // Expected state
                const expectedVisible = !isChatOpen;
                document.getElementById('expected-state').textContent = expectedVisible ? 'Visible' : 'Hidden';
                
                // Test result
                const isCorrect = (expectedVisible && isToggleVisible) || (!expectedVisible && !isToggleVisible);
                const resultElement = document.getElementById('test-result');
                if (isCorrect) {
                    resultElement.textContent = '✅ Correct';
                    resultElement.style.color = '#28a745';
                } else {
                    resultElement.textContent = '❌ Wrong';
                    resultElement.style.color = '#dc3545';
                }
            }
        }
        
        function clickToggle() {
            console.log('🔧 Clicking toggle...');
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
            }
        }
        
        function openPanel() {
            console.log('🔧 Opening panel...');
            if (window.chatbox) {
                window.chatbox.openChatPanel();
            }
        }
        
        function openWindow() {
            console.log('🔧 Opening window...');
            if (window.chatbox) {
                window.chatbox.openChatWindow();
            }
        }
        
        function closeAll() {
            console.log('🔧 Closing all...');
            if (window.chatbox) {
                window.chatbox.closeChatPanel();
                window.chatbox.closeChatWindow();
            }
        }
        
        function forceCheck() {
            console.log('🔧 Force checking...');
            if (window.chatbox && window.chatbox.startToggleVisibilityMonitor) {
                // Trigger the monitor function manually
                updateStatus();
            }
        }
        
        function forceHideToggle() {
            console.log('🔧 Force hiding toggle...');
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.style.display = 'none';
                alert('Toggle force hidden! Check if it stays hidden.');
            }
        }
        
        function debugInfo() {
            const chatPanel = document.getElementById('chat-panel');
            const chatWindow = document.getElementById('chat-window');
            const chatToggle = document.getElementById('chat-toggle');
            
            const info = {
                panelExists: !!chatPanel,
                panelOpen: chatPanel ? chatPanel.classList.contains('open') : false,
                windowExists: !!chatWindow,
                windowOpen: chatWindow ? chatWindow.classList.contains('open') : false,
                toggleExists: !!chatToggle,
                toggleStyle: chatToggle ? chatToggle.style.display : 'N/A',
                toggleComputed: chatToggle ? window.getComputedStyle(chatToggle).display : 'N/A',
                toggleVisible: chatToggle ? chatToggle.offsetParent !== null : false
            };
            
            console.log('🐛 Debug Info:', info);
            alert('Debug info logged to console. Check browser dev tools.');
        }
        
        // Auto-update status every 500ms
        setInterval(updateStatus, 500);
        
        // Initial status check
        setTimeout(updateStatus, 1000);
        
        console.log('🔍 Comprehensive toggle test loaded');
        console.log('Multiple solutions applied to ensure toggle hides when chat opens');
    </script>
</body>
</html>
