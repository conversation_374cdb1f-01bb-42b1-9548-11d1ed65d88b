<?php
session_start();
header('Content-Type: text/html; charset=utf-8');

echo "<h2>Chat API Fix Test</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; background: #e6ffe6; padding: 10px; border-radius: 4px; margin: 10px 0; }
.error { color: red; background: #ffe6e6; padding: 10px; border-radius: 4px; margin: 10px 0; }
.info { color: blue; background: #e6f3ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
</style>";

echo "<div class='info'>This test will verify that the chat API fix is working correctly.</div>";

// Check if user is logged in
if (empty($_SESSION)) {
    echo "<div class='error'>❌ You need to be logged in to test the chat API. Please log in first.</div>";
    exit;
}

// Try to find user ID
$user_id = null;
if (isset($_SESSION['id'])) {
    $user_id = $_SESSION['id'];
} elseif (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
} elseif (isset($_SESSION['userid'])) {
    $user_id = $_SESSION['userid'];
}

if (!$user_id) {
    echo "<div class='error'>❌ No user ID found in session. Available keys: " . implode(', ', array_keys($_SESSION)) . "</div>";
    exit;
}

echo "<div class='success'>✅ User ID found: $user_id</div>";

// Test 1: Direct API call
echo "<h3>Test 1: Direct API Call</h3>";
echo "<button onclick='testAPI()'>Test get_conversations API</button>";
echo "<div id='api-result'></div>";

// Test 2: Check database structure
echo "<h3>Test 2: Database Structure Check</h3>";
try {
    include '../db/dbconfig.php';
    mysqli_set_charset($connection, 'utf8mb4');
    
    if ($connection) {
        echo "<div class='success'>✅ Database connected</div>";
        
        // Check if tbl_chat exists and has correct structure
        $result = mysqli_query($connection, "DESCRIBE tbl_chat");
        if ($result) {
            echo "<div class='success'>✅ tbl_chat table exists</div>";
            echo "<h4>Table Structure:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
            while ($row = mysqli_fetch_assoc($result)) {
                echo "<tr>";
                echo "<td>{$row['Field']}</td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check for messages
            $count_result = mysqli_query($connection, "SELECT COUNT(*) as count FROM tbl_chat WHERE sender_id = $user_id OR recipient_id = $user_id");
            if ($count_result) {
                $count = mysqli_fetch_assoc($count_result)['count'];
                echo "<div class='info'>📊 You have $count messages in the database</div>";
                
                if ($count > 0) {
                    // Show recent conversations
                    $conv_result = mysqli_query($connection, "
                        SELECT DISTINCT 
                            CASE WHEN sender_id = $user_id THEN recipient_id ELSE sender_id END as other_user_id,
                            COUNT(*) as msg_count,
                            MAX(timestamp) as last_msg
                        FROM tbl_chat 
                        WHERE sender_id = $user_id OR recipient_id = $user_id
                        GROUP BY other_user_id
                        ORDER BY last_msg DESC
                        LIMIT 5
                    ");
                    
                    if ($conv_result && mysqli_num_rows($conv_result) > 0) {
                        echo "<h4>Your Recent Conversations:</h4>";
                        echo "<table border='1' style='border-collapse: collapse;'>";
                        echo "<tr><th>User ID</th><th>Messages</th><th>Last Message</th></tr>";
                        while ($conv = mysqli_fetch_assoc($conv_result)) {
                            echo "<tr>";
                            echo "<td>{$conv['other_user_id']}</td>";
                            echo "<td>{$conv['msg_count']}</td>";
                            echo "<td>{$conv['last_msg']}</td>";
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                }
            }
        } else {
            echo "<div class='error'>❌ tbl_chat table does not exist</div>";
            echo "<div class='info'>💡 You need to run the create_chat_tables.sql script</div>";
        }
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}

// Test 3: Manual function test
echo "<h3>Test 3: Manual Function Test</h3>";
echo "<button onclick='testManual()'>Test Fixed Function Manually</button>";
echo "<div id='manual-result'></div>";

?>

<script>
async function testAPI() {
    const resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = '<div style="color: blue; background: #e6f3ff; padding: 10px; border-radius: 4px;">Testing API...</div>';
    
    try {
        const response = await fetch('chat_api.php?action=get_conversations');
        const text = await response.text();
        
        let resultHTML = '<h4>API Response:</h4>';
        resultHTML += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        resultHTML += '<p><strong>Raw Response:</strong></p>';
        resultHTML += '<pre>' + text + '</pre>';
        
        // Try to parse as JSON
        try {
            const json = JSON.parse(text);
            resultHTML += '<div style="color: green; background: #e6ffe6; padding: 10px; border-radius: 4px; margin: 10px 0;">✅ Valid JSON Response!</div>';
            resultHTML += '<p><strong>Parsed Data:</strong></p>';
            resultHTML += '<pre>' + JSON.stringify(json, null, 2) + '</pre>';
            
            if (json.success) {
                resultHTML += '<div style="color: green; background: #e6ffe6; padding: 10px; border-radius: 4px;">🎉 API is working! Found ' + json.conversations.length + ' conversations.</div>';
            }
        } catch (e) {
            resultHTML += '<div style="color: red; background: #ffe6e6; padding: 10px; border-radius: 4px;">❌ Invalid JSON: ' + e.message + '</div>';
            
            // Check for specific errors
            if (text.includes('Fatal error')) {
                resultHTML += '<div style="color: orange; background: #fff3cd; padding: 10px; border-radius: 4px;">🔧 Still has PHP errors. Check the error message above.</div>';
            } else if (text.includes('Unknown column')) {
                resultHTML += '<div style="color: orange; background: #fff3cd; padding: 10px; border-radius: 4px;">🔧 Database column issue. The fix may not be complete.</div>';
            }
        }
        
        resultDiv.innerHTML = resultHTML;
        
    } catch (error) {
        resultDiv.innerHTML = '<div style="color: red; background: #ffe6e6; padding: 10px; border-radius: 4px;">❌ Network Error: ' + error.message + '</div>';
    }
}

async function testManual() {
    const resultDiv = document.getElementById('manual-result');
    resultDiv.innerHTML = '<div style="color: blue; background: #e6f3ff; padding: 10px; border-radius: 4px;">Testing manual function...</div>';
    
    try {
        const response = await fetch('test_manual_function.php');
        const text = await response.text();
        
        resultDiv.innerHTML = '<h4>Manual Test Result:</h4><pre>' + text + '</pre>';
        
    } catch (error) {
        resultDiv.innerHTML = '<div style="color: red; background: #ffe6e6; padding: 10px; border-radius: 4px;">❌ Error: ' + error.message + '</div>';
    }
}
</script>

<h3>Expected Results:</h3>
<div class="info">
<strong>✅ Success indicators:</strong><br>
• API returns valid JSON<br>
• No "Fatal error" or "Unknown column" messages<br>
• Response contains "success": true<br>
• Conversations array is present (even if empty)<br><br>

<strong>❌ Failure indicators:</strong><br>
• "Fatal error" messages<br>
• "Unknown column" errors<br>
• HTML error pages instead of JSON<br>
• Network errors<br><br>

<strong>🔧 If still failing:</strong><br>
• Make sure you're logged in<br>
• Run create_chat_tables.sql if tables are missing<br>
• Check that tbl_chat table has sender_id and recipient_id columns<br>
</div>
