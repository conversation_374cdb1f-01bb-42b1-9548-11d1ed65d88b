<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Simple Mobile Chat Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .info-box {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-content {
            height: 150vh;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .height-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
        }
        
        .test-button {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <div class="height-info" id="height-info">
        <div>Screen: <span id="screen-height"></span>px</div>
        <div>Viewport: <span id="viewport-height"></span>px</div>
        <div>Chat Height: <span id="chat-height">60vh</span></div>
        <div>Remaining: <span id="remaining-space"></span>px</div>
    </div>
    
    <button class="btn btn-primary test-button" onclick="openChat()">
        <i class="fa fa-comments"></i> Open Chat
    </button>
    
    <div class="info-box">
        <h4><i class="fa fa-mobile-alt"></i> Simple Mobile Chat Test</h4>
        <p><strong>Height Reduction Approach</strong></p>
        <p>Chat height reduced to 60vh (60% of viewport height)</p>
        <p>This leaves 40% of screen for input visibility</p>
    </div>
    
    <div class="test-content">
        <h2>Test Instructions</h2>
        <ol>
            <li><strong>Click the "Open Chat" button</strong> (top left)</li>
            <li><strong>Notice the chat height</strong> - it should only take 75% of screen</li>
            <li><strong>Try typing a message</strong> - input should be visible</li>
            <li><strong>Check the height info</strong> (top right) for measurements</li>
        </ol>
        
        <h3>What Should Happen:</h3>
        <ul>
            <li>✅ Chat opens from bottom</li>
            <li>✅ Chat takes only 60% of screen height</li>
            <li>✅ Input area is visible at bottom of chat</li>
            <li>✅ Keyboard doesn't cover input (40% space available)</li>
        </ul>
        
        <h3>Height Calculations:</h3>
        <div class="alert alert-info">
            <p><strong>Screen Height:</strong> <span id="screen-display"></span>px</p>
            <p><strong>Chat Height (60vh):</strong> <span id="chat-calc"></span>px</p>
            <p><strong>Available Space:</strong> <span id="space-calc"></span>px</p>
            <p><strong>Keyboard Space Needed:</strong> ~300px</p>
        </div>
        
        <div style="height: 200px; background: white; margin: 20px 0; padding: 20px; border-radius: 8px;">
            <h4>Sample Content</h4>
            <p>This content makes the page scrollable to test the chat overlay behavior.</p>
        </div>
        
        <div style="height: 200px; background: white; margin: 20px 0; padding: 20px; border-radius: 8px;">
            <h4>More Content</h4>
            <p>Additional content to ensure proper testing of the reduced height approach.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        function updateHeightInfo() {
            const screenHeight = screen.height;
            const viewportHeight = window.innerHeight;
            const chatHeight = Math.round(viewportHeight * 0.60); // 60vh
            const remainingSpace = viewportHeight - chatHeight;
            
            document.getElementById('screen-height').textContent = screenHeight;
            document.getElementById('viewport-height').textContent = viewportHeight;
            document.getElementById('remaining-space').textContent = remainingSpace;
            
            // Update calculations in content
            document.getElementById('screen-display').textContent = screenHeight;
            document.getElementById('chat-calc').textContent = chatHeight;
            document.getElementById('space-calc').textContent = remainingSpace;
            
            // Color code the remaining space
            const remainingSpan = document.getElementById('remaining-space');
            if (remainingSpace >= 300) {
                remainingSpan.style.color = '#28a745'; // Green - good
            } else if (remainingSpace >= 200) {
                remainingSpan.style.color = '#ffc107'; // Yellow - okay
            } else {
                remainingSpan.style.color = '#dc3545'; // Red - might be tight
            }
        }
        
        function openChat() {
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                
                // After chat opens, check the actual height
                setTimeout(() => {
                    const chatPanel = document.getElementById('chat-panel');
                    if (chatPanel) {
                        const rect = chatPanel.getBoundingClientRect();
                        console.log('Chat panel height:', rect.height);
                        console.log('Chat panel bottom:', rect.bottom);
                        console.log('Viewport height:', window.innerHeight);
                        console.log('Space below chat:', window.innerHeight - rect.bottom);
                    }
                }, 500);
            }
        }
        
        // Update on load and resize
        updateHeightInfo();
        window.addEventListener('resize', updateHeightInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateHeightInfo, 500);
        });
        
        console.log('Simple mobile test loaded');
        console.log('Approach: Reduce chat height to 60vh');
        console.log('Expected result: Input visible in remaining 40% space');
    </script>
</body>
</html>
