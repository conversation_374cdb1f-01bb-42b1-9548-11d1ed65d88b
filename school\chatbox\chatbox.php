<!-- Enhanced Pullout Chatbox -->
<div id="chatbox-container">
    <!-- Chat Toggle Button -->
    <div id="chat-toggle" class="chat-toggle">
        <i class="fa fa-comments"></i>
        <span class="chat-badge" id="chat-badge" style="display: none;">0</span>
    </div>

    <!-- Chat Panel -->
    <div id="chat-panel" class="chat-panel">
        <div class="chat-header">
            <h5 class="chat-title">
                <i class="fa fa-comments"></i> Chat
            </h5>
            <div class="chat-controls">
                <button class="btn btn-sm btn-outline-light" id="minimize-chat">
                    <i class="fa fa-minus"></i>
                </button>
                <button class="btn btn-sm btn-outline-light" id="close-chat">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        </div>

        <div class="chat-body">
            <!-- Search Section -->
            <div class="chat-search">
                <div class="input-group">
                    <input type="text" class="form-control form-control-sm" id="user-search"
                        placeholder="Search users to chat with...">
                    <button class="clear-search" type="button" id="clear-search" style="display: none;"
                        title="Clear search">
                        <i class="fa fa-times"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" type="button" id="search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- Chat Tabs -->
            <ul class="nav nav-tabs chat-tabs" id="chatTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="conversations-tab" data-bs-toggle="tab"
                        data-bs-target="#conversations-pane" type="button" role="tab">
                        Chats <span class="badge bg-danger ms-1" id="total-unread-badge" style="display: none;">0</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-pane"
                        type="button" role="tab">
                        Users
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content chat-tab-content" id="chatTabContent">
                <!-- Conversations Tab -->
                <div class="tab-pane fade show active" id="conversations-pane" role="tabpanel">
                    <div id="conversations-list" class="chat-list">
                        <div class="text-center p-3 text-muted">
                            <i class="fa fa-comments fa-2x mb-2"></i>
                            <p class="small">No conversations yet.<br>Search for users to start chatting!</p>
                        </div>
                    </div>
                </div>

                <!-- Users Tab -->
                <div class="tab-pane fade" id="users-pane" role="tabpanel">
                    <div id="users-list" class="chat-list">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Window -->
    <div id="chat-window" class="chat-window" style="display: none;">
        <div class="chat-window-header" id="chat-header">
            <div class="d-flex align-items-center">
                <img src="images/profile/thumbs/profile.png" alt="User" class="chat-user-avatar me-2"
                    id="chat-user-avatar">
                <div class="flex-grow-1">
                    <h6 class="mb-0" id="chat-user-name">User Name</h6>
                    <small class="text-muted" id="chat-user-status">Offline</small>
                </div>
                <div class="chat-window-controls">
                    <button class="btn btn-sm btn-outline-light" id="back-to-list">
                        <i class="fa fa-arrow-left"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-light" id="close-chat-window">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chat-messages">
            <!-- Messages will be loaded here -->
        </div>

        <div class="chat-input-area" id="message-input-area">
            <form id="message-form" enctype="multipart/form-data">
                <div class="input-group">
                    <button class="btn btn-outline-secondary btn-sm" type="button"
                        onclick="document.getElementById('file-input').click()">
                        <i class="fa fa-paperclip"></i>
                    </button>
                    <input type="text" class="form-control form-control-sm" id="message-input"
                        placeholder="Type your message..." maxlength="1000">
                    <button class="emoji-trigger btn btn-outline-secondary btn-sm" type="button" id="emoji-btn">
                        😀
                    </button>
                    <button class="btn btn-primary btn-sm" type="submit" id="send-btn">
                        <i class="fa fa-paper-plane"></i>
                    </button>
                </div>
                <input type="file" id="file-input" style="display: none;"
                    accept=".jpg,.jpeg,.png,.gif,.pdf,.txt,.doc,.docx" onchange="handleFileSelect(this)">
                <input type="hidden" id="current-recipient-id">
            </form>

            <!-- File Preview -->
            <div id="file-preview" class="file-preview" style="display: none;">
                <div class="alert alert-info alert-sm d-flex align-items-center">
                    <i class="fa fa-file me-2"></i>
                    <span id="file-name"></span>
                    <button type="button" class="btn-close btn-close-sm ms-auto"
                        onclick="clearFileSelection()"></button>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div id="typing-indicator" class="typing-indicator" style="display: none;">
                <small class="text-muted">
                    <i class="fa fa-circle-o-notch fa-spin"></i> Someone is typing...
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Online Status Indicator -->
<div id="online-status-indicator" class="online-status-indicator">
    <span class="status-dot status-online"></span>
    <span class="status-text">Online</span>
</div>

<!-- Notification Sound -->
<audio id="notification-sound" preload="auto">
    <source src="chatbox/sounds/notification.mp3" type="audio/mpeg">
    <source src="chatbox/sounds/notification.ogg" type="audio/ogg">
</audio>

<style>
/* Chatbox Styles */
#chatbox-container {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-toggle {
    position: fixed;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background: #007bff;
    color: white;
    padding: 15px 8px;
    border-radius: 8px 0 0 8px;
    cursor: pointer;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 10000;
}

.chat-toggle:hover {
    background: #0056b3;
    padding-left: 12px;
}

.chat-toggle i {
    font-size: 20px;
    display: block;
}

.chat-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.chat-panel {
    position: fixed;
    top: 0;
    right: -350px;
    width: 350px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    z-index: 9999;
}

.chat-panel.open {
    right: 0;
}

.chat-header {
    background: #007bff;
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #0056b3;
}

.chat-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chat-controls button {
    margin-left: 5px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-search {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.chat-tabs {
    border-bottom: 1px solid #e9ecef;
    background: white;
}

.chat-tabs .nav-link {
    font-size: 14px;
    padding: 10px 15px;
    border: none;
    color: #6c757d;
}

.chat-tabs .nav-link.active {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    background: none;
}

.chat-tab-content {
    flex: 1;
    overflow: hidden;
}

.chat-list {
    height: 100%;
    overflow-y: auto;
}

.chat-list-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
}

.chat-list-item:hover {
    background-color: #f8f9fa;
}

.chat-list-item.active {
    background-color: #e3f2fd;
    border-left: 3px solid #007bff;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.chat-item-content {
    flex: 1;
    min-width: 0;
}

.chat-item-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
    color: #333;
}

.chat-item-message {
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-item-meta {
    text-align: right;
    font-size: 11px;
    color: #6c757d;
}

.chat-item-time {
    display: block;
    margin-bottom: 4px;
}

.chat-item-badge {
    background: #dc3545;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-online {
    background: #28a745;
}

.status-away {
    background: #ffc107;
}

.status-busy {
    background: #dc3545;
}

.status-offline {
    background: #6c757d;
}

/* Chat Window */
.chat-window {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    z-index: 10001;
}

.chat-window.open {
    right: 0;
    display: flex !important;
}

.chat-window-header {
    background: #007bff;
    color: white;
    padding: 15px;
    border-bottom: 1px solid #0056b3;
}

.chat-user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
}

.chat-window-controls button {
    margin-left: 5px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.message.own {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin: 0 8px;
    object-fit: cover;
}

.message-content {
    max-width: 70%;
    background: white;
    padding: 10px 12px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
}

.message.own .message-content {
    background: #007bff;
    color: white;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

.message-time {
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
    text-align: right;
}

.message.own .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.message-file {
    display: flex;
    align-items: center;
    padding: 8px;
    background: #f1f3f4;
    border-radius: 8px;
    margin-top: 5px;
    cursor: pointer;
}

.message-file i {
    margin-right: 8px;
    color: #007bff;
}

.chat-input-area {
    padding: 15px;
    border-top: 1px solid #e9ecef;
    background: white;
}

.file-preview {
    margin-top: 10px;
}

.typing-indicator {
    padding: 5px 15px;
    background: #f8f9fa;
}

/* Online Status Indicator */
.online-status-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    padding: 8px 12px;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    font-size: 12px;
    z-index: 9998;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

/* Responsive */
@media (max-width: 768px) {

    .chat-panel,
    .chat-window {
        position: fixed;
        top: auto;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100vw !important;
        height: 40vh !important;
        /* Reduced to 50% - half screen only */
        max-width: 100vw;
        max-height: 40vh;
        /* Half screen max height */
        border-radius: 0;
        transform: translateY(100%);
        /* Slide up from bottom instead of right */
        transition: transform 0.3s ease;
        overflow: hidden;
    }

    .chat-panel.open,
    .chat-window.open {
        transform: translateY(0);
        /* Slide up animation */
        bottom: 0;
    }

    .chat-toggle {
        right: 10px;
        top: auto;
        bottom: 120px;
        /* Moved higher to avoid covering send button */
        transform: none;
        border-radius: 50%;
        padding: 12px;
        z-index: 10002;
    }

    /* Hide chat toggle when chat is open on mobile */
    .chat-panel.open~.chat-toggle,
    .chat-window.open~.chat-toggle {
        display: none !important;
    }

    /* Mobile chat header adjustments */
    .chat-header,
    .chat-window-header {
        padding: 10px 15px;
        min-height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-shrink: 0;
    }

    /* Mobile chat body adjustments */
    .chat-body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: calc(45vh);
        /* Reduced to 45% to leave room for input */
        max-height: 300px;
        /* Smaller maximum height */
        padding-bottom: 0;
    }

    /* Mobile message area */
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding: 10px;
        height: auto;
    }

    /* Mobile input area - add bottom spacing for visibility */
    .chat-input-area,
    #message-input-area {
        padding: 15px 15px 40px 15px;
        /* Extra bottom padding for visibility */
        margin-bottom: 30px;
        /* Additional bottom margin */
        border-top: 3px solid #007bff;
        /* Make border more visible */
        background: #f8f9fa;
        /* Light background to see it better */
        flex-shrink: 0;
        position: relative;
        /* Keep normal positioning */
        min-height: 80px;
        /* Ensure minimum height */
    }

    /* Ensure messages don't overlap input - add bottom spacing */
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 0;
        padding-bottom: 20px;
        /* Extra space at bottom of messages */
    }

    /* Mobile message styling */
    .message {
        margin-bottom: 10px;
        max-width: 85%;
    }

    .message.own {
        margin-left: auto;
        margin-right: 0;
    }

    .message-content {
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.4;
    }

    /* Mobile conversation list */
    .conversations-list {
        padding: 5px;
        overflow-y: auto;
        flex: 1;
    }

    .conversation-item {
        padding: 10px;
        margin-bottom: 5px;
    }

    /* Mobile search */
    .chat-search {
        padding: 10px;
        flex-shrink: 0;
    }

    /* Mobile message actions */
    .message-actions {
        position: static;
        opacity: 1;
        visibility: visible;
        margin-top: 5px;
        justify-content: flex-end;
    }

    /* Mobile edit form */
    .message-edit-form {
        margin-top: 5px;
        padding: 10px;
    }

    .edit-input-container {
        flex-direction: column;
        gap: 10px;
    }

    .message-edit-input {
        min-height: 80px;
    }

    /* Make input field more visible on mobile */
    #message-input {
        min-height: 50px !important;
        font-size: 16px !important;
        /* Prevent zoom on iOS */
        border: 2px solid #007bff !important;
        border-radius: 25px !important;
        padding: 15px !important;
        background: white !important;
    }

    /* Mobile input group styling */
    .input-group {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        width: 100%;
    }

    .input-group .form-control {
        flex: 1;
        min-height: 44px;
        padding: 12px;
        border-radius: 22px;
        border: 1px solid #ced4da;
        font-size: 16px;
        /* Prevents zoom on iOS */
        resize: none;
        max-height: 120px;
        overflow-y: auto;
    }

    .input-group .btn {
        min-width: 44px;
        min-height: 44px;
        border-radius: 50%;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Safe area support for newer phones */
    @supports (padding: max(0px)) {

        .chat-panel,
        .chat-window {
            padding-bottom: max(env(safe-area-inset-bottom), 0px);
        }
    }
}

/* Scrollbar Styling */
.chat-list::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-list::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-list::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Drag and Drop Styles */
.chat-input-area.drag-over {
    background-color: #e3f2fd !important;
    border: 2px dashed #007bff !important;
    border-radius: 8px;
}

.drag-drop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.1);
    border: 2px dashed #007bff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #007bff;
    z-index: 1000;
}

/* Enhanced File Preview */
.file-preview .alert {
    margin-bottom: 0;
    padding: 8px 12px;
}

.file-preview img {
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* File message enhancements */
.message-file {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    margin-top: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.message-file:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-file i {
    font-size: 20px;
    margin-right: 10px;
    color: #007bff;
}

.message-file .file-info {
    flex: 1;
}

.message-file .file-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
}

.message-file .file-size {
    font-size: 12px;
    color: #6c757d;
}

/* Image message preview */
.message-image-container {
    position: relative;
    display: inline-block;
    max-width: 250px;
    margin-top: 5px;
}

.message-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-image:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
    border-radius: 8px;
    color: white;
    font-size: 20px;
}

.message-image-container:hover .image-overlay {
    opacity: 1;
}

.file-info-small {
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
    display: flex;
    justify-content: space-between;
}

.file-info-small .file-name {
    font-weight: 500;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Search Enhancements */
.chat-search input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.chat-list-item mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 2px;
    border-radius: 2px;
}

.search-results-count {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 8px 15px;
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

/* Loading states */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Search input with clear button */
.chat-search .input-group {
    position: relative;
}

.chat-search .clear-search {
    position: absolute;
    right: 35px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    z-index: 10;
    padding: 2px;
    border-radius: 2px;
}

.chat-search .clear-search:hover {
    color: #495057;
    background-color: #f8f9fa;
}

/* Emoticon Picker Styles */
.emoticon-picker {
    position: fixed;
    width: 320px;
    height: 300px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000000 !important;
    overflow: hidden;
}

.emoticon-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 8px;
}

.emoticon-categories {
    display: flex;
    justify-content: space-around;
}

.category-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 6px;
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.category-btn:hover {
    background-color: #e9ecef;
}

.category-btn.active {
    background-color: #007bff;
    color: white;
}

.emoticon-content {
    height: 240px;
    overflow-y: auto;
    padding: 8px;
}

.emoticon-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
}

.emoticon-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 4px;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    min-height: 36px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.emoticon-btn:hover {
    background-color: #f8f9fa;
    transform: scale(1.1);
}

.emoticon-btn:active {
    background-color: #e9ecef;
    transform: scale(0.95);
}

/* Message Actions */
.message {
    position: relative;
    transition: background-color 0.2s ease;
}

.message.own:hover {
    background-color: rgba(0, 123, 255, 0.05);
    border-radius: 8px;
}

.message.editable {
    cursor: pointer;
}

.message.editable:hover {
    background-color: rgba(0, 123, 255, 0.08);
}

.message:hover .message-actions {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.message-actions {
    position: absolute;
    top: -8px;
    right: 15px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 4px 6px;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.8);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    gap: 2px;
}

.message.own .message-actions {
    right: auto;
    left: 15px;
}

/* Always show actions on mobile/touch devices */
@media (hover: none) {
    .message-actions {
        opacity: 0.7;
        visibility: visible;
        transform: scale(1);
    }

    .message:hover .message-actions,
    .message-actions:hover {
        opacity: 1;
    }
}

.message-action-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 8px 10px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    color: #6c757d;
    transition: all 0.2s ease;
    margin: 0 2px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-action-btn:hover {
    background-color: #e9ecef;
    color: #495057;
    transform: scale(1.1);
}

.message-action-btn.edit:hover {
    color: #007bff;
    background-color: #e3f2fd;
    border-color: #007bff;
}

.message-action-btn.delete:hover {
    color: #dc3545;
    background-color: #ffebee;
    border-color: #dc3545;
}

/* Message Edit Mode */
.message-edit-form {
    margin-top: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.edit-input-container {
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.message-edit-input {
    flex: 1;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 14px;
    resize: none;
    min-height: 60px;
}

.edit-emoji-btn {
    background: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
    height: fit-content;
}

.edit-emoji-btn:hover {
    background: #e9ecef;
}

.message-edit-actions {
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.message-edit-actions button {
    padding: 4px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
}

.btn-save {
    background: #28a745;
    color: white;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

/* Message Status Indicators */
.message-edited {
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
    margin-top: 2px;
}

.message-deleted {
    opacity: 0.6;
    font-style: italic;
    color: #6c757d;
}

.message-deleted .message-text {
    background: #f8f9fa;
    border: 1px dashed #ced4da;
    padding: 8px;
    border-radius: 4px;
}

/* Delete Confirmation Modal */
.delete-confirmation {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    padding: 20px;
    z-index: 10001;
    min-width: 300px;
}

.delete-confirmation h6 {
    margin-bottom: 15px;
    color: #333;
}

.delete-options {
    margin: 15px 0;
}

.delete-option {
    display: block;
    margin: 8px 0;
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
}

.delete-option:hover {
    background-color: #f8f9fa;
}

.delete-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 15px;
}

/* Emoji button in input */
.emoji-trigger {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.emoji-trigger:hover {
    background-color: #f8f9fa;
}

/* Mobile emoji picker styles */
@media (max-width: 768px) {
    .emoticon-picker {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        top: auto !important;
        width: 100vw !important;
        height: 50vh !important;
        max-height: 400px !important;
        border-radius: 15px 15px 0 0 !important;
        z-index: 1000001 !important;
    }

    .emoticon-header {
        padding: 15px 10px 10px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 15px 15px 0 0;
    }

    .emoticon-categories {
        display: flex;
        justify-content: space-around;
        gap: 5px;
    }

    .category-btn {
        padding: 10px;
        font-size: 20px;
        min-width: 44px;
        min-height: 44px;
    }

    .emoticon-content {
        height: calc(50vh - 80px);
        max-height: 320px;
        overflow-y: auto;
        padding: 10px;
    }

    .emoticon-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 8px;
    }

    .emoticon-btn {
        padding: 12px;
        font-size: 24px;
        min-width: 44px;
        min-height: 44px;
        border-radius: 8px;
    }

    /* Mobile backdrop */
    .emoji-picker-backdrop {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Mobile input adjustments */
    .input-group {
        flex-wrap: nowrap;
    }

    .form-control {
        font-size: 16px;
        /* Prevents zoom on iOS */
    }

    /* Mobile file preview */
    .file-preview {
        max-width: 100%;
        margin: 5px 0;
    }

    .file-preview img {
        max-width: 100%;
        height: auto;
    }

    /* Mobile notification styles */
    .notification {
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        max-width: none !important;
    }
}
</style>