<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Search API Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .api-response { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .info { border-left: 4px solid #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>🔍 User Search API Test</h2>
        
        <div class="alert alert-info">
            <h5>Testing User Search Functionality</h5>
            <p>This will test the chat API's user search feature to identify why you're getting "Network error".</p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>API Tests</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testDirectAPI()">Test Direct API Call</button>
                <button class="btn btn-success" onclick="testWithSearch()">Test With Search Term</button>
                <button class="btn btn-info" onclick="testSession()">Test Session</button>
                <button class="btn btn-warning" onclick="testDatabase()">Test Database</button>
            </div>
        </div>
        
        <div id="results"></div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>Manual User Search Test</h5>
            </div>
            <div class="card-body">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="search-input" placeholder="Search for users...">
                    <button class="btn btn-outline-secondary" onclick="searchUsers()">Search</button>
                </div>
                <div id="search-results"></div>
            </div>
        </div>
    </div>

    <script>
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `api-response ${type}`;
            div.innerHTML = `<h6>${title}</h6><pre>${content}</pre>`;
            results.appendChild(div);
        }

        async function testDirectAPI() {
            addResult('Testing Direct API Call...', 'Calling chat_api.php?action=get_users', 'info');
            
            try {
                const response = await fetch('chat_api.php?action=get_users');
                const text = await response.text();
                
                addResult('Raw Response', `Status: ${response.status}\nContent-Type: ${response.headers.get('content-type')}\n\nResponse:\n${text}`, 
                    response.ok ? 'success' : 'error');
                
                try {
                    const json = JSON.parse(text);
                    addResult('Parsed JSON', JSON.stringify(json, null, 2), 'success');
                } catch (e) {
                    addResult('JSON Parse Error', e.message, 'error');
                }
            } catch (error) {
                addResult('Network Error', error.message, 'error');
            }
        }

        async function testWithSearch() {
            addResult('Testing With Search Term...', 'Calling chat_api.php?action=get_users&search=test', 'info');
            
            try {
                const response = await fetch('chat_api.php?action=get_users&search=test');
                const text = await response.text();
                
                addResult('Search Response', `Status: ${response.status}\n\nResponse:\n${text}`, 
                    response.ok ? 'success' : 'error');
                
                try {
                    const json = JSON.parse(text);
                    addResult('Search Results', `Found ${json.users ? json.users.length : 0} users\n\n${JSON.stringify(json, null, 2)}`, 'success');
                } catch (e) {
                    addResult('JSON Parse Error', e.message, 'error');
                }
            } catch (error) {
                addResult('Network Error', error.message, 'error');
            }
        }

        async function testSession() {
            addResult('Testing Session...', 'Checking session status', 'info');
            
            try {
                const response = await fetch('check_session.php');
                const text = await response.text();
                
                addResult('Session Check', text, response.ok ? 'success' : 'error');
            } catch (error) {
                addResult('Session Error', error.message, 'error');
            }
        }

        async function testDatabase() {
            addResult('Testing Database...', 'Checking database connection and tables', 'info');
            
            try {
                const response = await fetch('verify_setup.php');
                const text = await response.text();
                
                // Extract just the relevant parts
                const parser = new DOMParser();
                const doc = parser.parseFromString(text, 'text/html');
                const steps = doc.querySelectorAll('.step');
                
                let summary = '';
                steps.forEach((step, index) => {
                    const title = step.querySelector('h3')?.textContent || `Step ${index + 1}`;
                    const content = step.textContent.replace(/\s+/g, ' ').trim();
                    summary += `${title}:\n${content.substring(0, 200)}...\n\n`;
                });
                
                addResult('Database Check', summary || text.substring(0, 500), 'info');
            } catch (error) {
                addResult('Database Error', error.message, 'error');
            }
        }

        async function searchUsers() {
            const searchTerm = document.getElementById('search-input').value;
            const resultsDiv = document.getElementById('search-results');
            
            resultsDiv.innerHTML = '<div class="text-info">Searching...</div>';
            
            try {
                const url = `chat_api.php?action=get_users${searchTerm ? '&search=' + encodeURIComponent(searchTerm) : ''}`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success && data.users) {
                    let html = `<div class="alert alert-success">Found ${data.users.length} users:</div>`;
                    
                    if (data.users.length > 0) {
                        html += '<div class="list-group">';
                        data.users.forEach(user => {
                            html += `
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${user.name}</h6>
                                        <small class="text-muted">ID: ${user.id}</small>
                                    </div>
                                    <p class="mb-1">${user.email || 'No email'}</p>
                                    <small class="text-muted">Status: ${user.status || 'offline'}</small>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Network Error: ${error.message}</div>`;
            }
        }

        // Auto-run basic test on load
        window.addEventListener('load', () => {
            setTimeout(testDirectAPI, 1000);
        });
    </script>
</body>
</html>
