-- =====================================================
-- QUICK FIX FOR ONLINE HOSTING ISSUES
-- =====================================================
-- This script fixes common issues when moving from localhost to online hosting
-- Run this if your chat history is not showing online

-- Set proper charset
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- =====================================================
-- 1. ENSURE TABLES EXIST WITH CORRECT STRUCTURE
-- =====================================================

-- Create tbl_chat if it doesn't exist or fix structure
CREATE TABLE IF NOT EXISTS `tbl_chat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_type` enum('text','image','file') DEFAULT 'text',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0',
  `is_edited` tinyint(1) DEFAULT '0',
  `edited_at` timestamp NULL DEFAULT NULL,
  `original_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `deleted_for` enum('none', 'sender', 'both') DEFAULT 'none',
  PRIMARY KEY (`id`),
  KEY `idx_sender_recipient` (`sender_id`,`recipient_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create tbl_chat_files if it doesn't exist
CREATE TABLE IF NOT EXISTS `tbl_chat_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `upload_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. FIX EXISTING TABLE STRUCTURE
-- =====================================================

-- Update existing tbl_chat table
ALTER TABLE `tbl_chat` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add missing columns if they don't exist
ALTER TABLE `tbl_chat` 
ADD COLUMN IF NOT EXISTS `is_edited` TINYINT(1) DEFAULT 0 AFTER `is_deleted`,
ADD COLUMN IF NOT EXISTS `edited_at` TIMESTAMP NULL AFTER `is_edited`,
ADD COLUMN IF NOT EXISTS `original_message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL AFTER `edited_at`,
ADD COLUMN IF NOT EXISTS `deleted_for` ENUM('none', 'sender', 'both') DEFAULT 'none' AFTER `original_message`;

-- Fix message column encoding
ALTER TABLE `tbl_chat` 
MODIFY COLUMN `message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `original_message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL;

-- Add essential indexes if they don't exist
ALTER TABLE `tbl_chat` 
ADD INDEX IF NOT EXISTS `idx_sender_recipient` (`sender_id`,`recipient_id`),
ADD INDEX IF NOT EXISTS `idx_timestamp` (`timestamp`),
ADD INDEX IF NOT EXISTS `idx_is_read` (`is_read`),
ADD INDEX IF NOT EXISTS `idx_conversation` (`sender_id`, `recipient_id`, `timestamp`),
ADD INDEX IF NOT EXISTS `idx_unread_messages` (`recipient_id`, `is_read`, `timestamp`);

-- =====================================================
-- 3. FIX COMMON HOSTING ISSUES
-- =====================================================

-- Fix tbl_users table encoding (if it exists)
ALTER TABLE `tbl_users` 
MODIFY COLUMN `fname` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
MODIFY COLUMN `lname` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Update tbl_chat_files if it exists
ALTER TABLE `tbl_chat_files` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `tbl_chat_files`
MODIFY COLUMN `original_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `file_path` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

-- =====================================================
-- 4. CREATE ESSENTIAL SUPPORT TABLES
-- =====================================================

-- Create conversations table for better performance
CREATE TABLE IF NOT EXISTS `tbl_conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user1_id` int(11) NOT NULL,
  `user2_id` int(11) NOT NULL,
  `last_message_id` int(11) DEFAULT NULL,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `user1_unread_count` int(11) DEFAULT '0',
  `user2_unread_count` int(11) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_conversation` (`user1_id`,`user2_id`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user status table
CREATE TABLE IF NOT EXISTS `tbl_user_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `status` enum('online','away','offline') DEFAULT 'offline',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_status` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create notifications table
CREATE TABLE IF NOT EXISTS `tbl_chat_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `notification_type` enum('message','mention','system') DEFAULT 'message',
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_unread` (`user_id`,`is_read`),
  KEY `idx_message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. INSERT DEFAULT DATA FOR EXISTING USERS
-- =====================================================

-- Insert default user status for existing users (ignore if already exists)
INSERT IGNORE INTO tbl_user_status (user_id, status, last_seen, last_activity)
SELECT id, 'offline', NOW(), NOW() FROM tbl_users;

-- =====================================================
-- 6. VERIFY AND DISPLAY RESULTS
-- =====================================================

-- Show table status
SELECT 
    TABLE_NAME,
    TABLE_COLLATION,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('tbl_chat', 'tbl_users', 'tbl_chat_files', 'tbl_conversations', 'tbl_user_status', 'tbl_chat_notifications');

-- Show recent chat data
SELECT 
    'Total Messages' as Info,
    COUNT(*) as Count
FROM tbl_chat
UNION ALL
SELECT 
    'Total Users with Messages' as Info,
    COUNT(DISTINCT sender_id) as Count
FROM tbl_chat
UNION ALL
SELECT 
    'Messages Today' as Info,
    COUNT(*) as Count
FROM tbl_chat 
WHERE DATE(timestamp) = CURDATE();

-- Success message
SELECT 'Database structure updated successfully! Chat history should now be visible.' as Status;
