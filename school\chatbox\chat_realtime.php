<?php
session_start();
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');
include '../db/dbconfig.php';

// Set UTF-8 encoding for database connection
mysqli_set_charset($connection, 'utf8mb4');

// Check if user is logged in (try different session variable names)
$user_id = null;
if (isset($_SESSION['id'])) {
    $user_id = $_SESSION['id'];
} elseif (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
} elseif (isset($_SESSION['userid'])) {
    $user_id = $_SESSION['userid'];
}

if (!$user_id) {
    http_response_code(401);
    echo json_encode([
        'error' => 'Unauthorized',
        'debug' => 'Session variables: ' . json_encode(array_keys($_SESSION))
    ]);
    exit;
}
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'poll':
        pollForUpdates($connection, $user_id);
        break;
    case 'heartbeat':
        updateHeartbeat($connection, $user_id);
        break;
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
}

function pollForUpdates($connection, $user_id) {
    $last_check = $_GET['last_check'] ?? date('Y-m-d H:i:s', strtotime('-1 minute'));
    $timeout = 30; // 30 seconds long polling
    $start_time = time();
    
    do {
        // Check for new messages
        $new_messages = checkNewMessages($connection, $user_id, $last_check);
        
        // Check for status updates
        $status_updates = checkStatusUpdates($connection, $user_id, $last_check);
        
        // Check for new notifications
        $notifications = checkNotifications($connection, $user_id, $last_check);
        
        if (!empty($new_messages) || !empty($status_updates) || !empty($notifications)) {
            echo json_encode([
                'success' => true,
                'timestamp' => date('Y-m-d H:i:s'),
                'new_messages' => $new_messages,
                'status_updates' => $status_updates,
                'notifications' => $notifications
            ]);
            return;
        }
        
        // Sleep for a short time before checking again
        usleep(500000); // 0.5 seconds
        
    } while ((time() - $start_time) < $timeout);
    
    // No updates found within timeout
    echo json_encode([
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'new_messages' => [],
        'status_updates' => [],
        'notifications' => []
    ]);
}

function checkNewMessages($connection, $user_id, $last_check) {
    $last_message_id = $_GET['last_message_id'] ?? 0;

    // Build query with both timestamp and message ID filtering for better reliability
    $whereConditions = [
        "c.recipient_id = ?",
        "c.timestamp > ?",
        "c.is_deleted = 0",
        "c.is_read = 0",
        "c.sender_id != ?"
    ];

    $params = [$user_id, $last_check, $user_id];
    $types = "isi";

    // Add message ID filter if provided
    if ($last_message_id > 0) {
        $whereConditions[] = "c.id > ?";
        $params[] = $last_message_id;
        $types .= "i";
    }

    $whereClause = implode(" AND ", $whereConditions);

    $stmt = $connection->prepare("
        SELECT c.*, u.fname, u.lname, u.image,
               cf.original_name, cf.file_path, cf.file_size, cf.file_type
        FROM tbl_chat c
        JOIN tbl_users u ON c.sender_id = u.id
        LEFT JOIN tbl_chat_files cf ON c.id = cf.message_id
        WHERE $whereClause
        ORDER BY c.id ASC
        LIMIT 50
    ");
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => (int)$row['id'],
            'sender_id' => (int)$row['sender_id'],
            'recipient_id' => (int)$row['recipient_id'],
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'timestamp' => $row['timestamp'],
            'is_read' => (int)$row['is_read'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'sender_image' => $row['image'] ?: 'profile.png',
            'file_name' => $row['original_name'],
            'file_path' => $row['file_path'],
            'file_size' => $row['file_size'] ? (int)$row['file_size'] : null,
            'file_type' => $row['file_type']
        ];
    }

    return $messages;
}

function checkStatusUpdates($connection, $user_id, $last_check) {
    // Get status updates for users the current user has conversations with
    $stmt = $connection->prepare("
        SELECT DISTINCT us.user_id, us.status, us.last_seen, u.fname, u.lname
        FROM tbl_user_status us
        JOIN tbl_users u ON us.user_id = u.id
        JOIN tbl_conversations c ON (
            (c.participant_1 = ? AND c.participant_2 = us.user_id) OR
            (c.participant_2 = ? AND c.participant_1 = us.user_id)
        )
        WHERE us.last_activity > ?
        ORDER BY us.last_activity DESC
    ");
    $stmt->bind_param("iis", $user_id, $user_id, $last_check);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $updates = [];
    while ($row = $result->fetch_assoc()) {
        $updates[] = [
            'user_id' => $row['user_id'],
            'status' => $row['status'],
            'last_seen' => $row['last_seen'],
            'name' => $row['fname'] . ' ' . $row['lname']
        ];
    }
    
    return $updates;
}

function checkNotifications($connection, $user_id, $last_check) {
    $stmt = $connection->prepare("
        SELECT cn.*, c.message, c.sender_id, u.fname, u.lname
        FROM tbl_chat_notifications cn
        JOIN tbl_chat c ON cn.message_id = c.id
        JOIN tbl_users u ON c.sender_id = u.id
        WHERE cn.user_id = ? AND cn.created_at > ? AND cn.is_read = 0
        ORDER BY cn.created_at DESC
    ");
    $stmt->bind_param("is", $user_id, $last_check);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        $notifications[] = [
            'id' => $row['id'],
            'message_id' => $row['message_id'],
            'sender_id' => $row['sender_id'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'message' => $row['message'],
            'created_at' => $row['created_at']
        ];
    }
    
    return $notifications;
}

function updateHeartbeat($connection, $user_id) {
    // Update user's last activity to show they're still online
    $stmt = $connection->prepare("
        UPDATE tbl_user_status 
        SET last_activity = NOW() 
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to update heartbeat']);
    }
}

// Auto-update user status to away if inactive for too long
function checkAutoAway($connection, $user_id) {
    $stmt = $connection->prepare("
        SELECT cs.auto_away_minutes 
        FROM tbl_chat_settings cs 
        WHERE cs.user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $settings = $result->fetch_assoc();
    
    $away_minutes = $settings['auto_away_minutes'] ?? 15;
    
    $stmt = $connection->prepare("
        UPDATE tbl_user_status 
        SET status = 'away' 
        WHERE user_id = ? 
        AND status = 'online' 
        AND last_activity < DATE_SUB(NOW(), INTERVAL ? MINUTE)
    ");
    $stmt->bind_param("ii", $user_id, $away_minutes);
    $stmt->execute();
}

// Call auto-away check
if ($action === 'poll') {
    checkAutoAway($connection, $user_id);
}
?>
