// Emoticon Picker for Chatbox
class EmoticonPicker {
    constructor() {
        console.log('EmoticonPicker constructor called');
        this.emoticons = {
            'smileys': {
                'name': 'Smileys & People',
                'emojis': [
                    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
                    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
                    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
                    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
                    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
                    '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
                    '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
                    '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
                    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
                    '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾'
                ]
            },
            'animals': {
                'name': 'Animals & Nature',
                'emojis': [
                    '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
                    '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
                    '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
                    '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜',
                    '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕',
                    '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳',
                    '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛',
                    '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖'
                ]
            },
            'food': {
                'name': 'Food & Drink',
                'emojis': [
                    '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
                    '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
                    '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔',
                    '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈',
                    '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟',
                    '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕',
                    '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙',
                    '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦'
                ]
            },
            'activities': {
                'name': 'Activities',
                'emojis': [
                    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
                    '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
                    '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️',
                    '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️', '🤼', '🤸', '⛹️', '🤺',
                    '🏇', '🧘', '🏄', '🏊', '🤽', '🚣', '🧗', '🚵', '🚴', '🏆',
                    '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️', '🎫', '🎟️', '🎪',
                    '🤹', '🎭', '🩰', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶',
                    '🥁', '🪘', '🎹', '🥊', '🎯', '🎳', '🎮', '🎰', '🧩', '🃏'
                ]
            },
            'objects': {
                'name': 'Objects',
                'emojis': [
                    '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
                    '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
                    '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️',
                    '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋',
                    '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴',
                    '💶', '💷', '🪙', '💰', '💳', '💎', '⚖️', '🪜', '🧰', '🔧',
                    '🔨', '⚒️', '🛠️', '⛏️', '🪓', '🪚', '🔩', '⚙️', '🪤', '🧲',
                    '🔫', '💣', '🧨', '🪓', '🔪', '🗡️', '⚔️', '🛡️', '🚬', '⚰️'
                ]
            },
            'hearts': {
                'name': 'Hearts',
                'emojis': [
                    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
                    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️',
                    '💋', '💌', '💐', '🌹', '🌷', '🌺', '🌸', '🌼', '🌻', '💒'
                ]
            }
        };
        
        this.currentCategory = 'smileys';
        this.isVisible = false;
        this.targetInput = null;
        
        this.createPicker();
        this.bindEvents();
    }
    
    createPicker() {
        console.log('Creating emoticon picker...');
        const picker = document.createElement('div');
        picker.id = 'emoticon-picker';
        picker.className = 'emoticon-picker';
        picker.style.display = 'none';

        picker.innerHTML = `
            <div class="emoticon-header">
                <div class="emoticon-categories">
                    ${Object.keys(this.emoticons).map(key => `
                        <button class="category-btn ${key === this.currentCategory ? 'active' : ''}"
                                data-category="${key}" title="${this.emoticons[key].name}">
                            ${this.getCategoryIcon(key)}
                        </button>
                    `).join('')}
                </div>
            </div>
            <div class="emoticon-content">
                <div class="emoticon-grid" id="emoticon-grid">
                    ${this.renderEmoticons(this.currentCategory)}
                </div>
            </div>
        `;

        document.body.appendChild(picker);
        this.picker = picker;
        console.log('Emoticon picker created and added to DOM:', picker);
    }
    
    getCategoryIcon(category) {
        const icons = {
            'smileys': '😀',
            'animals': '🐶',
            'food': '🍎',
            'activities': '⚽',
            'objects': '💻',
            'hearts': '❤️'
        };
        return icons[category] || '😀';
    }
    
    renderEmoticons(category) {
        return this.emoticons[category].emojis.map(emoji => 
            `<button class="emoticon-btn" data-emoji="${emoji}">${emoji}</button>`
        ).join('');
    }
    
    bindEvents() {
        // Category switching
        this.picker.addEventListener('click', (e) => {
            if (e.target.classList.contains('category-btn')) {
                const category = e.target.dataset.category;
                this.switchCategory(category);
            } else if (e.target.classList.contains('emoticon-btn')) {
                const emoji = e.target.dataset.emoji;
                this.insertEmoji(emoji);
            }
        });
        
        // Close picker when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isVisible && !this.picker.contains(e.target) && !e.target.closest('.emoji-trigger')) {
                this.hide();
            }
        });
    }
    
    switchCategory(category) {
        this.currentCategory = category;
        
        // Update active category button
        this.picker.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === category);
        });
        
        // Update emoticon grid
        document.getElementById('emoticon-grid').innerHTML = this.renderEmoticons(category);
    }
    
    show(targetInput, position) {
        this.targetInput = targetInput;
        this.isVisible = true;
        this.picker.style.display = 'block';

        // Position the picker
        if (position) {
            // Make sure picker is visible first to get correct dimensions
            this.picker.style.visibility = 'hidden';
            this.picker.style.display = 'block';

            const pickerHeight = this.picker.offsetHeight;
            const pickerWidth = this.picker.offsetWidth;

            // Position above the button, but ensure it stays within viewport
            let top = position.y - pickerHeight - 10;
            let left = position.x - (pickerWidth / 2);

            // Adjust if picker would go off screen
            if (top < 10) {
                top = position.y + 40; // Show below button instead
            }
            if (left < 10) {
                left = 10;
            }
            if (left + pickerWidth > window.innerWidth - 10) {
                left = window.innerWidth - pickerWidth - 10;
            }

            this.picker.style.left = left + 'px';
            this.picker.style.top = top + 'px';
            this.picker.style.visibility = 'visible';
        }

        console.log('Emoji picker shown at position:', position);
    }
    
    hide() {
        this.isVisible = false;
        this.picker.style.display = 'none';
        this.targetInput = null;
    }
    
    insertEmoji(emoji) {
        if (this.targetInput) {
            const cursorPos = this.targetInput.selectionStart;
            const textBefore = this.targetInput.value.substring(0, cursorPos);
            const textAfter = this.targetInput.value.substring(this.targetInput.selectionEnd);
            
            this.targetInput.value = textBefore + emoji + textAfter;
            this.targetInput.selectionStart = this.targetInput.selectionEnd = cursorPos + emoji.length;
            this.targetInput.focus();
        }
        
        this.hide();
    }
}

// Initialize emoticon picker when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing emoticon picker...');
    window.emoticonPicker = new EmoticonPicker();
    console.log('Emoticon picker initialized:', window.emoticonPicker);
});
