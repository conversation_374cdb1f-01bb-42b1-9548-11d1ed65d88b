// Emoticon Picker for Chatbox
class EmoticonPicker {
    constructor() {
        this.emoticons = {
            'smileys': {
                'name': 'Smileys & People',
                'emojis': [
                    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
                    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
                    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
                    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
                    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
                    '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
                    '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
                    '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
                    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
                    '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾'
                ]
            },
            'animals': {
                'name': 'Animals & Nature',
                'emojis': [
                    '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
                    '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
                    '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
                    '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜',
                    '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕',
                    '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳',
                    '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛',
                    '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖'
                ]
            },
            'food': {
                'name': 'Food & Drink',
                'emojis': [
                    '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
                    '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
                    '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔',
                    '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈',
                    '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟',
                    '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕',
                    '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙',
                    '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦'
                ]
            },
            'activities': {
                'name': 'Activities',
                'emojis': [
                    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
                    '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
                    '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️',
                    '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️', '🤼', '🤸', '⛹️', '🤺',
                    '🏇', '🧘', '🏄', '🏊', '🤽', '🚣', '🧗', '🚵', '🚴', '🏆',
                    '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️', '🎫', '🎟️', '🎪',
                    '🤹', '🎭', '🩰', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶',
                    '🥁', '🪘', '🎹', '🥊', '🎯', '🎳', '🎮', '🎰', '🧩', '🃏'
                ]
            },
            'objects': {
                'name': 'Objects',
                'emojis': [
                    '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
                    '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
                    '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️',
                    '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋',
                    '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴',
                    '💶', '💷', '🪙', '💰', '💳', '💎', '⚖️', '🪜', '🧰', '🔧',
                    '🔨', '⚒️', '🛠️', '⛏️', '🪓', '🪚', '🔩', '⚙️', '🪤', '🧲',
                    '🔫', '💣', '🧨', '🪓', '🔪', '🗡️', '⚔️', '🛡️', '🚬', '⚰️'
                ]
            },
            'hearts': {
                'name': 'Hearts',
                'emojis': [
                    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
                    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️',
                    '💋', '💌', '💐', '🌹', '🌷', '🌺', '🌸', '🌼', '🌻', '💒'
                ]
            }
        };
        
        this.currentCategory = 'smileys';
        this.isVisible = false;
        this.targetInput = null;
        
        this.createPicker();
        this.bindEvents();
    }
    
    createPicker() {
        const picker = document.createElement('div');
        picker.id = 'emoticon-picker';
        picker.className = 'emoticon-picker';
        picker.style.display = 'none';
        
        picker.innerHTML = `
            <div class="emoticon-header">
                <div class="emoticon-categories">
                    ${Object.keys(this.emoticons).map(key => `
                        <button class="category-btn ${key === this.currentCategory ? 'active' : ''}" 
                                data-category="${key}" title="${this.emoticons[key].name}">
                            ${this.getCategoryIcon(key)}
                        </button>
                    `).join('')}
                </div>
            </div>
            <div class="emoticon-content">
                <div class="emoticon-grid" id="emoticon-grid">
                    ${this.renderEmoticons(this.currentCategory)}
                </div>
            </div>
        `;
        
        document.body.appendChild(picker);
        this.picker = picker;
    }
    
    getCategoryIcon(category) {
        const icons = {
            'smileys': '😀',
            'animals': '🐶',
            'food': '🍎',
            'activities': '⚽',
            'objects': '💻',
            'hearts': '❤️'
        };
        return icons[category] || '😀';
    }
    
    renderEmoticons(category) {
        return this.emoticons[category].emojis.map(emoji => 
            `<button class="emoticon-btn" data-emoji="${emoji}">${emoji}</button>`
        ).join('');
    }
    
    bindEvents() {
        // Category switching
        this.picker.addEventListener('click', (e) => {
            if (e.target.classList.contains('category-btn')) {
                const category = e.target.dataset.category;
                this.switchCategory(category);
            } else if (e.target.classList.contains('emoticon-btn')) {
                const emoji = e.target.dataset.emoji;
                this.insertEmoji(emoji);
            }
        });
        
        // Close picker when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isVisible && !this.picker.contains(e.target) && !e.target.closest('.emoji-trigger')) {
                this.hide();
            }
        });
    }
    
    switchCategory(category) {
        this.currentCategory = category;
        
        // Update active category button
        this.picker.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === category);
        });
        
        // Update emoticon grid
        document.getElementById('emoticon-grid').innerHTML = this.renderEmoticons(category);
    }
    
    show(targetInput, position) {
        this.targetInput = targetInput;
        this.isVisible = true;
        this.picker.style.display = 'block';
        
        // Position the picker
        if (position) {
            this.picker.style.left = position.x + 'px';
            this.picker.style.top = (position.y - this.picker.offsetHeight - 10) + 'px';
        }
    }
    
    hide() {
        this.isVisible = false;
        this.picker.style.display = 'none';
        this.targetInput = null;
    }
    
    insertEmoji(emoji) {
        if (this.targetInput) {
            const cursorPos = this.targetInput.selectionStart;
            const textBefore = this.targetInput.value.substring(0, cursorPos);
            const textAfter = this.targetInput.value.substring(this.targetInput.selectionEnd);
            
            this.targetInput.value = textBefore + emoji + textAfter;
            this.targetInput.selectionStart = this.targetInput.selectionEnd = cursorPos + emoji.length;
            this.targetInput.focus();
        }
        
        this.hide();
    }
}

// Initialize emoticon picker
window.emoticonPicker = new EmoticonPicker();
