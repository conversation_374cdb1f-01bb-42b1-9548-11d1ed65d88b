/**
 * Force Mobile Input Visibility
 * Aggressive fix to ensure chat input is always visible on mobile
 */

(function() {
    'use strict';
    
    // Check if mobile
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                     window.innerWidth <= 768;
    
    if (!isMobile) return;
    
    console.log('🔧 Force Mobile Input Fix - Starting...');
    
    // Aggressive CSS injection
    function injectMobileCSS() {
        // Remove any existing mobile fix styles
        const existingStyle = document.getElementById('force-mobile-input-style');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        const style = document.createElement('style');
        style.id = 'force-mobile-input-style';
        style.innerHTML = `
            /* FORCE MOBILE INPUT VISIBILITY */
            @media screen and (max-width: 768px) {
                /* Force input area to bottom */
                .chat-input-area,
                #message-input-area,
                div[id*="input"],
                div[class*="input"] {
                    position: fixed !important;
                    bottom: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    width: 100% !important;
                    z-index: 99999 !important;
                    background: white !important;
                    border-top: 2px solid #007bff !important;
                    padding: 15px !important;
                    box-shadow: 0 -5px 15px rgba(0,0,0,0.2) !important;
                    transform: translateY(0) !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: block !important;
                    max-height: none !important;
                    overflow: visible !important;
                }
                
                /* Force input field styling */
                #message-input,
                input[id*="message"],
                textarea[id*="message"] {
                    width: 100% !important;
                    min-height: 50px !important;
                    font-size: 16px !important;
                    padding: 15px !important;
                    border: 2px solid #007bff !important;
                    border-radius: 25px !important;
                    background: white !important;
                    color: black !important;
                    resize: none !important;
                    box-sizing: border-box !important;
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    position: relative !important;
                    z-index: 100000 !important;
                }
                
                /* Force input group styling */
                .input-group {
                    display: flex !important;
                    align-items: center !important;
                    gap: 10px !important;
                    width: 100% !important;
                }
                
                /* Force button styling */
                .chat-input-area button,
                #message-input-area button {
                    min-width: 50px !important;
                    min-height: 50px !important;
                    border-radius: 50% !important;
                    background: #007bff !important;
                    color: white !important;
                    border: none !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    flex-shrink: 0 !important;
                }
                
                /* Adjust chat messages to make room */
                .chat-messages {
                    padding-bottom: 120px !important;
                    margin-bottom: 0 !important;
                }
                
                /* Force chat panel height */
                .chat-panel,
                #chat-panel {
                    height: 100vh !important;
                    max-height: 100vh !important;
                    display: flex !important;
                    flex-direction: column !important;
                }
                
                /* Force chat body to take remaining space */
                .chat-body {
                    flex: 1 !important;
                    overflow: hidden !important;
                    display: flex !important;
                    flex-direction: column !important;
                }
                
                /* Hide any overlapping elements */
                .chat-input-area ~ *,
                #message-input-area ~ * {
                    z-index: 1 !important;
                }
            }
            
            /* Extra aggressive rules for stubborn cases */
            @media screen and (max-width: 768px) {
                body.mobile-chat-open {
                    overflow: hidden !important;
                    position: fixed !important;
                    width: 100% !important;
                    height: 100% !important;
                }
                
                .mobile-input-visible {
                    position: fixed !important;
                    bottom: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    z-index: 999999 !important;
                    background: white !important;
                    padding: 20px !important;
                    border-top: 3px solid #007bff !important;
                    box-shadow: 0 -10px 20px rgba(0,0,0,0.3) !important;
                }
            }
        `;
        
        document.head.appendChild(style);
        console.log('✅ Mobile CSS injected');
    }
    
    // Force input visibility
    function forceInputVisible() {
        const inputArea = document.querySelector('.chat-input-area, #message-input-area');
        const messageInput = document.getElementById('message-input');
        
        if (inputArea) {
            inputArea.classList.add('mobile-input-visible');
            inputArea.style.cssText = `
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                z-index: 999999 !important;
                background: white !important;
                padding: 20px !important;
                border-top: 3px solid #007bff !important;
                box-shadow: 0 -10px 20px rgba(0,0,0,0.3) !important;
                width: 100% !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            `;
            console.log('✅ Input area forced visible');
        }
        
        if (messageInput) {
            messageInput.style.cssText = `
                width: 100% !important;
                min-height: 50px !important;
                font-size: 16px !important;
                padding: 15px !important;
                border: 2px solid #007bff !important;
                border-radius: 25px !important;
                background: white !important;
                color: black !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 1000000 !important;
            `;
            console.log('✅ Message input forced visible');
        }
    }
    
    // Monitor for chat opening
    function monitorChatOpen() {
        const chatToggle = document.getElementById('chat-toggle');
        const chatPanel = document.getElementById('chat-panel');
        
        if (chatToggle) {
            chatToggle.addEventListener('click', function() {
                console.log('🔧 Chat toggle clicked');
                setTimeout(() => {
                    forceInputVisible();
                    document.body.classList.add('mobile-chat-open');
                }, 500);
            });
        }
        
        // Monitor for chat panel opening
        if (chatPanel) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        if (chatPanel.classList.contains('open')) {
                            console.log('🔧 Chat panel opened');
                            setTimeout(forceInputVisible, 100);
                        }
                    }
                });
            });
            
            observer.observe(chatPanel, {
                attributes: true,
                attributeFilter: ['class']
            });
        }
    }
    
    // Force input focus handling
    function setupInputHandling() {
        document.addEventListener('click', function(e) {
            if (e.target.id === 'message-input' || e.target.closest('.chat-input-area')) {
                console.log('🔧 Input area clicked');
                setTimeout(() => {
                    forceInputVisible();
                    const messageInput = document.getElementById('message-input');
                    if (messageInput) {
                        messageInput.focus();
                        messageInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }, 100);
            }
        });
    }
    
    // Periodic check to ensure input stays visible
    function startPeriodicCheck() {
        setInterval(() => {
            const chatPanel = document.getElementById('chat-panel');
            if (chatPanel && chatPanel.classList.contains('open')) {
                const inputArea = document.querySelector('.chat-input-area, #message-input-area');
                if (inputArea) {
                    const rect = inputArea.getBoundingClientRect();
                    if (rect.bottom > window.innerHeight || rect.top < 0) {
                        console.log('🔧 Input not visible, forcing...');
                        forceInputVisible();
                    }
                }
            }
        }, 2000);
    }
    
    // Initialize everything
    function init() {
        console.log('🔧 Initializing force mobile input fix...');
        
        injectMobileCSS();
        
        // Wait for DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                monitorChatOpen();
                setupInputHandling();
                startPeriodicCheck();
            });
        } else {
            monitorChatOpen();
            setupInputHandling();
            startPeriodicCheck();
        }
        
        // Also check when chatbox is loaded
        const checkInterval = setInterval(() => {
            if (document.getElementById('chat-toggle')) {
                monitorChatOpen();
                setupInputHandling();
                clearInterval(checkInterval);
            }
        }, 1000);
    }
    
    // Expose global function for manual fixing
    window.forceMobileInputVisible = function() {
        console.log('🔧 Manual force input visible called');
        forceInputVisible();
    };
    
    // Start
    init();
    
    console.log('🔧 Force Mobile Input Fix - Loaded');
    
})();
