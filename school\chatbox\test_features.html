<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox Features Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .emoji-trigger {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .test-message {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            position: relative;
            border-left: 4px solid #007bff;
        }
        
        .test-message:hover .message-actions {
            opacity: 1;
            visibility: visible;
        }
        
        .message-actions {
            position: absolute;
            top: 5px;
            right: 10px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
        }
        
        .message-action-btn {
            background: white;
            border: 1px solid #dee2e6;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 2px;
            font-size: 12px;
        }
        
        .message-action-btn:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chatbox Features Test</h1>
        
        <div class="test-section">
            <h3>Emoji Picker Test</h3>
            <p>Click the emoji button to test the picker:</p>
            <div class="input-group">
                <input type="text" class="test-input" id="test-input" placeholder="Type here and click emoji button...">
                <button class="emoji-trigger" id="test-emoji-btn">😀</button>
            </div>
            <button class="btn btn-primary mt-2" onclick="testEmojiPicker()">Test Emoji Picker</button>
            <button class="btn btn-secondary mt-2" onclick="checkEmoticonPicker()">Check Picker Status</button>
        </div>
        
        <div class="test-section">
            <h3>Message Actions Test</h3>
            <p>Hover over the message below to see edit/delete buttons:</p>
            <div class="test-message" data-message-id="test-123">
                <div class="message-content">
                    <div class="message-text">This is a test message that you can edit or delete</div>
                    <div class="message-time">Just now</div>
                </div>
                <div class="message-actions">
                    <button class="message-action-btn edit edit-message-btn" data-message-id="test-123">
                        <i class="fa fa-edit"></i> Edit
                    </button>
                    <button class="message-action-btn delete delete-message-btn" data-message-id="test-123">
                        <i class="fa fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Debug Information</h3>
            <button class="btn btn-info" onclick="showDebugInfo()">Show Debug Info</button>
            <pre id="debug-output" class="mt-3" style="background: #f8f9fa; padding: 10px; border-radius: 4px;"></pre>
        </div>
    </div>

    <!-- Include the emoticon picker -->
    <script src="emoticons.js"></script>
    
    <script>
        // Set up test emoji button
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            const testEmojiBtn = document.getElementById('test-emoji-btn');
            const testInput = document.getElementById('test-input');
            
            if (testEmojiBtn && testInput) {
                testEmojiBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Test emoji button clicked');
                    
                    if (window.emoticonPicker) {
                        const rect = testEmojiBtn.getBoundingClientRect();
                        window.emoticonPicker.show(testInput, {
                            x: rect.left + rect.width / 2,
                            y: rect.top
                        });
                    } else {
                        console.error('Emoticon picker not available');
                    }
                });
            }
            
            // Test message actions
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('edit-message-btn')) {
                    alert('Edit button clicked for message: ' + e.target.dataset.messageId);
                } else if (e.target.classList.contains('delete-message-btn')) {
                    alert('Delete button clicked for message: ' + e.target.dataset.messageId);
                }
            });
        });
        
        function testEmojiPicker() {
            console.log('Manual emoji picker test');
            if (window.emoticonPicker) {
                const testInput = document.getElementById('test-input');
                window.emoticonPicker.show(testInput, {
                    x: 300,
                    y: 200
                });
            } else {
                alert('Emoticon picker not found!');
            }
        }
        
        function checkEmoticonPicker() {
            const status = {
                emoticonPickerExists: !!window.emoticonPicker,
                pickerElement: !!document.getElementById('emoticon-picker'),
                testInputExists: !!document.getElementById('test-input'),
                testButtonExists: !!document.getElementById('test-emoji-btn')
            };
            
            document.getElementById('debug-output').textContent = JSON.stringify(status, null, 2);
        }
        
        function showDebugInfo() {
            const info = {
                windowEmoticonPicker: !!window.emoticonPicker,
                emoticonPickerElement: !!document.getElementById('emoticon-picker'),
                bodyChildren: document.body.children.length,
                scripts: Array.from(document.scripts).map(s => s.src || 'inline'),
                errors: window.errors || 'none'
            };
            
            document.getElementById('debug-output').textContent = JSON.stringify(info, null, 2);
        }
        
        // Catch any errors
        window.addEventListener('error', function(e) {
            if (!window.errors) window.errors = [];
            window.errors.push(e.message);
            console.error('Page error:', e);
        });
    </script>
</body>
</html>
