// Enhanced Notification System for Chatbox
class ChatNotificationHandler {
    constructor() {
        this.notificationPermission = 'default';
        this.soundEnabled = true;
        this.desktopEnabled = true;
        this.notificationQueue = [];
        this.activeNotifications = new Map();
        this.notificationSound = null;

        this.init();
    }

    init() {
        this.checkNotificationSupport();
        this.requestPermission();
        this.createNotificationSound();
        this.bindVisibilityEvents();
        this.enableAudioOnUserInteraction();
    }

    enableAudioOnUserInteraction() {
        // Enable audio context on first user interaction (required by browsers)
        const enableAudio = () => {
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            // Remove listeners after first interaction
            document.removeEventListener('click', enableAudio);
            document.removeEventListener('keydown', enableAudio);
            document.removeEventListener('touchstart', enableAudio);
        };

        document.addEventListener('click', enableAudio);
        document.addEventListener('keydown', enableAudio);
        document.addEventListener('touchstart', enableAudio);
    }

    checkNotificationSupport() {
        if (!('Notification' in window)) {
            console.warn('This browser does not support desktop notifications');
            this.desktopEnabled = false;
        }

        if (!('serviceWorker' in navigator)) {
            console.warn('Service Worker not supported');
        }
    }

    async requestPermission() {
        if ('Notification' in window) {
            this.notificationPermission = await Notification.requestPermission();
        }
    }

    createNotificationSound() {
        // Create notification sound using Web Audio API for better browser support
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

            // Create a simple notification beep
            const createBeep = (frequency, duration) => {
                // Resume audio context if suspended (required by some browsers)
                if (this.audioContext.state === 'suspended') {
                    this.audioContext.resume();
                }

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.audioContext.destination);

                oscillator.frequency.value = frequency;
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + duration);
            };

            this.playNotificationSound = () => {
                if (this.soundEnabled) {
                    try {
                        createBeep(800, 0.15);
                        setTimeout(() => createBeep(600, 0.15), 150);
                    } catch (error) {
                        console.warn('Error playing notification sound:', error);
                    }
                }
            };
        } catch (error) {
            console.warn('Could not create audio context for notifications:', error);
            // Fallback to HTML5 audio
            this.createFallbackSound();
        }
    }

    createFallbackSound() {
        // Create a simple HTML5 audio fallback
        this.playNotificationSound = () => {
            if (this.soundEnabled) {
                try {
                    // Create a simple beep using data URL
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    audio.volume = 0.3;
                    audio.play().catch(e => console.log('Could not play fallback sound'));
                } catch (error) {
                    console.warn('Fallback sound also failed:', error);
                }
            }
        };
    }

    bindVisibilityEvents() {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // Clear notifications when user returns to tab
                setTimeout(() => {
                    this.clearAllNotifications();
                }, 1000); // Small delay to allow chat window to open if clicked
            }
        });

        // Clear notifications when user interacts with the page
        document.addEventListener('click', () => {
            // Clear old notifications after user interaction
            setTimeout(() => {
                this.clearOldNotifications();
            }, 500);
        });
    }

    clearOldNotifications() {
        const notifications = document.querySelectorAll('.chat-in-app-notification');
        notifications.forEach(notification => {
            const age = Date.now() - (notification.dataset.timestamp || 0);
            if (age > 10000) { // Remove notifications older than 10 seconds
                this.removeNotification(notification);
            }
        });
    }

    showNotification(options) {
        const {
            title,
            message,
            senderId,
            senderName,
            senderImage,
            messageId,
            timestamp,
            type = 'message'
        } = options;

        // Don't show notification if chat window is open and focused on sender
        if (!document.hidden && window.chatbox && window.chatbox.currentRecipientId == senderId) {
            // Still mark as read even if not showing notification
            if (messageId) {
                this.markNotificationAsRead(messageId);
            }
            // Clear any existing notifications for this sender
            this.clearNotificationsForSender(senderId);
            return;
        }

        // Check if we already showed notification for this message
        if (messageId && this.hasShownNotification(messageId)) {
            return;
        }

        // Mark that we've shown this notification
        if (messageId) {
            this.markNotificationShown(messageId);
        }

        // Play sound
        this.playNotificationSound();

        // Show desktop notification
        if (this.desktopEnabled && this.notificationPermission === 'granted') {
            this.showDesktopNotification({
                title: senderName || title,
                body: message,
                icon: senderImage ? `images/profile/thumbs/${senderImage}` : 'images/prime_hrm_logo.png',
                tag: `chat-${senderId}-${messageId}`,
                data: {
                    senderId,
                    senderName,
                    messageId,
                    timestamp,
                    type
                }
            });
        }

        // Show in-app notification
        this.showInAppNotification(options);

        // Update badge counts
        this.updateBadgeCounts();
    }

    hasShownNotification(messageId) {
        if (!this.shownNotifications) {
            this.shownNotifications = new Set();
        }
        return this.shownNotifications.has(messageId);
    }

    markNotificationShown(messageId) {
        if (!this.shownNotifications) {
            this.shownNotifications = new Set();
        }
        this.shownNotifications.add(messageId);

        // Clean up old notifications (keep only last 100)
        if (this.shownNotifications.size > 100) {
            const oldestEntries = Array.from(this.shownNotifications).slice(0, 50);
            oldestEntries.forEach(id => this.shownNotifications.delete(id));
        }
    }

    showDesktopNotification(options) {
        try {
            const notification = new Notification(options.title, {
                body: options.body,
                icon: options.icon,
                tag: options.tag,
                requireInteraction: false,
                silent: true // We handle sound ourselves
            });

            // Store notification reference
            this.activeNotifications.set(options.tag, notification);

            // Handle click
            notification.onclick = () => {
                window.focus();
                if (window.chatbox && options.data.senderId) {
                    // Open chat with sender
                    window.chatbox.showChatWindow(
                        options.data.senderId,
                        options.data.senderName,
                        '',
                        'online'
                    );

                    // Mark notification as read
                    if (options.data.messageId) {
                        this.markNotificationAsRead(options.data.messageId);
                    }
                }
                notification.close();
                this.activeNotifications.delete(options.tag);
            };

            // Auto close after 5 seconds
            setTimeout(() => {
                if (this.activeNotifications.has(options.tag)) {
                    notification.close();
                    this.activeNotifications.delete(options.tag);
                }
            }, 5000);

        } catch (error) {
            console.error('Error showing desktop notification:', error);
        }
    }

    showInAppNotification(options) {
        const {
            title,
            message,
            senderId,
            senderName,
            senderImage,
            messageId,
            type = 'message'
        } = options;

        // Check if notification for this message already exists
        const existingNotification = document.querySelector(`[data-message-id="${messageId}"]`);
        if (existingNotification) {
            return; // Don't show duplicate notification
        }

        // Create notification element
        const notificationEl = document.createElement('div');
        notificationEl.className = 'chat-in-app-notification';
        notificationEl.setAttribute('data-message-id', messageId);
        notificationEl.setAttribute('data-sender-id', senderId);
        notificationEl.setAttribute('data-timestamp', Date.now());
        notificationEl.innerHTML = `
            <div class="notification-content">
                <img src="images/profile/thumbs/${senderImage || 'profile.png'}" alt="${senderName}" class="notification-avatar">
                <div class="notification-text">
                    <div class="notification-title">${senderName || title}</div>
                    <div class="notification-message">${message}</div>
                </div>
                <button class="notification-close">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        `;

        // Add click handler for opening chat
        notificationEl.addEventListener('click', (e) => {
            if (!e.target.closest('.notification-close')) {
                if (window.chatbox && senderId) {
                    window.chatbox.showChatWindow(senderId, senderName, senderImage, 'online');
                    // Mark notification as read
                    this.markNotificationAsRead(messageId);
                }
                this.removeNotification(notificationEl);
            }
        });

        // Add close button handler
        const closeBtn = notificationEl.querySelector('.notification-close');
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.removeNotification(notificationEl);
        });

        // Add to page
        let container = document.getElementById('chat-notifications-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'chat-notifications-container';
            container.className = 'chat-notifications-container';
            document.body.appendChild(container);
        }

        container.appendChild(notificationEl);

        // Animate in
        setTimeout(() => notificationEl.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notificationEl.parentNode) {
                this.removeNotification(notificationEl);
            }
        }, 5000);
    }

    removeNotification(notificationEl) {
        if (notificationEl && notificationEl.parentNode) {
            notificationEl.classList.remove('show');
            setTimeout(() => {
                if (notificationEl.parentNode) {
                    notificationEl.remove();
                }
            }, 300);
        }
    }

    markNotificationAsRead(messageId) {
        // Send request to mark notification as read
        if (messageId) {
            fetch('chatbox/chat_api.php?action=mark_notification_read', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message_id: messageId })
            }).catch(error => console.error('Error marking notification as read:', error));
        }
    }

    clearNotificationsForSender(senderId) {
        // Clear in-app notifications for this sender
        const notifications = document.querySelectorAll(`[data-sender-id="${senderId}"]`);
        notifications.forEach(notification => {
            this.removeNotification(notification);
        });

        // Clear desktop notifications for this sender
        this.activeNotifications.forEach((notification, tag) => {
            if (tag.includes(`chat-${senderId}`)) {
                notification.close();
                this.activeNotifications.delete(tag);
            }
        });
    }

    updateBadgeCounts() {
        // This will be called by the main chatbox to update badge counts
        if (window.chatbox) {
            window.chatbox.updateUnreadCount();
        }
    }

    clearAllNotifications() {
        // Clear desktop notifications
        this.activeNotifications.forEach(notification => {
            notification.close();
        });
        this.activeNotifications.clear();

        // Clear in-app notifications
        const container = document.getElementById('chat-notifications-container');
        if (container) {
            container.innerHTML = '';
        }
    }

    setSettings(settings) {
        this.soundEnabled = settings.sound_notifications;
        this.desktopEnabled = settings.desktop_notifications;
    }

    // Browser notification with fallback
    async showBrowserNotification(title, options = {}) {
        // Try modern notification API first
        if ('Notification' in window && this.notificationPermission === 'granted') {
            return new Notification(title, options);
        }

        // Fallback to in-app notification
        this.showInAppNotification({
            title: title,
            message: options.body || '',
            senderImage: options.icon
        });
    }

    // Test notification
    testNotification() {
        this.showNotification({
            title: 'Test Notification',
            message: 'This is a test notification from the chat system.',
            senderName: 'System',
            messageId: 'test-' + Date.now(),
            type: 'test'
        });
    }

    // Test sound
    testSound() {
        console.log('Testing notification sound...');
        this.playNotificationSound();
    }
}

// CSS for in-app notifications
const notificationStyles = `
<style>
.chat-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    pointer-events: none;
}

.chat-in-app-notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    max-width: 350px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    border-left: 4px solid #007bff;
}

.chat-in-app-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 12px;
    cursor: pointer;
}

.notification-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.notification-text {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.notification-message {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    margin-left: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notification-close:hover {
    background-color: #f1f1f1;
    color: #666;
}

/* Animation for notification badge */
@keyframes notificationPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.chat-badge.new-message {
    animation: notificationPulse 0.6s ease-in-out;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .chat-notifications-container {
        left: 10px;
        right: 10px;
        top: 10px;
    }

    .chat-in-app-notification {
        max-width: none;
    }
}
</style>
`;

// Inject styles
document.head.insertAdjacentHTML('beforeend', notificationStyles);

// Initialize notification handler
window.chatNotificationHandler = new ChatNotificationHandler();