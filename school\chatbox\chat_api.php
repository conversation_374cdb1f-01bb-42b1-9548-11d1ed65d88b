<?php
session_start();
header('Content-Type: application/json; charset=utf-8');
include '../db/dbconfig.php';

// Set UTF-8 encoding for database connection
mysqli_set_charset($connection, 'utf8mb4');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in (try different session variable names)
$user_id = null;
if (isset($_SESSION['id'])) {
    $user_id = $_SESSION['id'];
} elseif (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
} elseif (isset($_SESSION['userid'])) {
    $user_id = $_SESSION['userid'];
}

if (!$user_id) {
    http_response_code(401);
    echo json_encode([
        'error' => 'Unauthorized',
        'debug' => 'Session variables: ' . json_encode(array_keys($_SESSION)),
        'session_id_exists' => isset($_SESSION['id']),
        'session_user_id_exists' => isset($_SESSION['user_id']),
        'session_userid_exists' => isset($_SESSION['userid'])
    ]);
    exit;
}
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'send_message':
        sendMessage($connection, $user_id);
        break;
    case 'get_messages':
        getMessages($connection, $user_id);
        break;
    case 'get_conversations':
        getConversations($connection, $user_id);
        break;
    case 'get_users':
        getUsers($connection, $user_id);
        break;
    case 'update_status':
        updateUserStatus($connection, $user_id);
        break;
    case 'get_online_users':
        getOnlineUsers($connection);
        break;
    case 'mark_as_read':
        markAsRead($connection, $user_id);
        break;
    case 'get_unread_count':
        getUnreadCount($connection, $user_id);
        break;
    case 'upload_file':
        uploadFile($connection, $user_id);
        break;
    case 'get_user_settings':
        getUserSettings($connection, $user_id);
        break;
    case 'update_settings':
        updateSettings($connection, $user_id);
        break;
    case 'mark_notification_read':
        markNotificationAsRead($connection, $user_id);
        break;
    case 'edit_message':
        editMessage($connection, $user_id);
        break;
    case 'delete_message':
        deleteMessage($connection, $user_id);
        break;
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
}

function sendMessage($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true, 512, JSON_UNESCAPED_UNICODE);
    $recipient_id = $input['recipient_id'] ?? '';
    $message = trim($input['message'] ?? '');
    $message_type = $input['message_type'] ?? 'text';

    // Ensure proper UTF-8 encoding for emojis
    $message = mb_convert_encoding($message, 'UTF-8', 'UTF-8');
    
    if (empty($recipient_id) || empty($message)) {
        echo json_encode(['success' => false, 'error' => 'Missing required fields']);
        return;
    }
    
    // Insert message
    $stmt = $connection->prepare("INSERT INTO tbl_chat (sender_id, recipient_id, message, message_type) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("iiss", $user_id, $recipient_id, $message, $message_type);
    
    if ($stmt->execute()) {
        $message_id = $connection->insert_id;
        
        // Update or create conversation
        updateConversation($connection, $user_id, $recipient_id, $message_id);
        
        // Create notification
        createNotification($connection, $recipient_id, $message_id);
        
        echo json_encode(['success' => true, 'message_id' => $message_id]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to send message']);
    }
}

function getMessages($connection, $user_id) {
    $recipient_id = $_GET['recipient_id'] ?? '';
    $last_message_id = $_GET['last_message_id'] ?? 0;
    
    if (empty($recipient_id)) {
        echo json_encode(['success' => false, 'error' => 'Recipient ID required']);
        return;
    }
    
    $stmt = $connection->prepare("
        SELECT c.*, u.fname, u.lname, u.image,
               cf.original_name, cf.file_path, cf.file_size, cf.file_type
        FROM tbl_chat c
        JOIN tbl_users u ON c.sender_id = u.id
        LEFT JOIN tbl_chat_files cf ON c.id = cf.message_id
        WHERE ((c.sender_id = ? AND c.recipient_id = ?) OR (c.sender_id = ? AND c.recipient_id = ?))
        AND (c.is_deleted = 0 OR c.deleted_for = 'both')
        AND (c.deleted_for != 'sender' OR c.sender_id != ?)
        AND c.id > ?
        ORDER BY c.timestamp ASC
    ");
    $stmt->bind_param("iiiiii", $user_id, $recipient_id, $recipient_id, $user_id, $user_id, $last_message_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'sender_id' => $row['sender_id'],
            'recipient_id' => $row['recipient_id'],
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'timestamp' => $row['timestamp'],
            'is_read' => $row['is_read'],
            'is_edited' => $row['is_edited'],
            'edited_at' => $row['edited_at'],
            'deleted_for' => $row['deleted_for'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'sender_image' => $row['image'] ?: 'profile.png',
            'file_name' => $row['original_name'],
            'file_path' => $row['file_path'],
            'file_size' => $row['file_size'],
            'file_type' => $row['file_type']
        ];
    }
    
    echo json_encode(['success' => true, 'messages' => $messages]);
}

function getConversations($connection, $user_id) {
    $stmt = $connection->prepare("
        SELECT DISTINCT 
            CASE 
                WHEN c.participant_1 = ? THEN c.participant_2 
                ELSE c.participant_1 
            END as other_user_id,
            u.fname, u.lname, u.image,
            us.status, us.last_seen,
            lm.message as last_message,
            lm.timestamp as last_message_time,
            COUNT(CASE WHEN chat.is_read = 0 AND chat.recipient_id = ? THEN 1 END) as unread_count
        FROM tbl_conversations c
        JOIN tbl_users u ON (
            CASE 
                WHEN c.participant_1 = ? THEN c.participant_2 
                ELSE c.participant_1 
            END = u.id
        )
        LEFT JOIN tbl_user_status us ON u.id = us.user_id
        LEFT JOIN tbl_chat lm ON c.last_message_id = lm.id
        LEFT JOIN tbl_chat chat ON (
            (chat.sender_id = u.id AND chat.recipient_id = ?) OR 
            (chat.sender_id = ? AND chat.recipient_id = u.id)
        )
        WHERE c.participant_1 = ? OR c.participant_2 = ?
        GROUP BY other_user_id, u.fname, u.lname, u.image, us.status, us.last_seen, lm.message, lm.timestamp
        ORDER BY c.last_activity DESC
    ");
    $stmt->bind_param("iiiiiii", $user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $conversations = [];
    while ($row = $result->fetch_assoc()) {
        $conversations[] = [
            'user_id' => $row['other_user_id'],
            'name' => $row['fname'] . ' ' . $row['lname'],
            'image' => $row['image'] ?: 'profile.png',
            'status' => $row['status'] ?: 'offline',
            'last_seen' => $row['last_seen'],
            'last_message' => $row['last_message'],
            'last_message_time' => $row['last_message_time'],
            'unread_count' => $row['unread_count']
        ];
    }
    
    echo json_encode(['success' => true, 'conversations' => $conversations]);
}

function getUsers($connection, $user_id) {
    $search = $_GET['search'] ?? '';
    $whereClause = "WHERE u.id != ?";
    $params = [$user_id];
    $types = "i";
    
    if (!empty($search)) {
        $whereClause .= " AND (u.fname LIKE ? OR u.lname LIKE ? OR u.email LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        $types .= "sss";
    }
    
    $stmt = $connection->prepare("
        SELECT u.id, u.fname, u.lname, u.email, u.position, u.image,
               us.status, us.last_seen
        FROM tbl_users u
        LEFT JOIN tbl_user_status us ON u.id = us.user_id
        $whereClause
        ORDER BY u.fname, u.lname
        LIMIT 50
    ");
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = [
            'id' => $row['id'],
            'name' => $row['fname'] . ' ' . $row['lname'],
            'email' => $row['email'],
            'position' => $row['position'],
            'image' => $row['image'] ?: 'profile.png',
            'status' => $row['status'] ?: 'offline',
            'last_seen' => $row['last_seen']
        ];
    }
    
    echo json_encode(['success' => true, 'users' => $users]);
}

function updateUserStatus($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $status = $input['status'] ?? 'online';
    
    $stmt = $connection->prepare("
        INSERT INTO tbl_user_status (user_id, status, last_activity) 
        VALUES (?, ?, NOW()) 
        ON DUPLICATE KEY UPDATE 
        status = VALUES(status), 
        last_activity = NOW(),
        last_seen = CASE WHEN VALUES(status) = 'offline' THEN NOW() ELSE last_seen END
    ");
    $stmt->bind_param("is", $user_id, $status);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to update status']);
    }
}

function getOnlineUsers($connection) {
    $stmt = $connection->prepare("
        SELECT u.id, u.fname, u.lname, u.image, us.status, us.last_seen
        FROM tbl_users u
        JOIN tbl_user_status us ON u.id = us.user_id
        WHERE us.status IN ('online', 'away', 'busy')
        ORDER BY us.last_activity DESC
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    
    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = [
            'id' => $row['id'],
            'name' => $row['fname'] . ' ' . $row['lname'],
            'image' => $row['image'] ?: 'profile.png',
            'status' => $row['status'],
            'last_seen' => $row['last_seen']
        ];
    }
    
    echo json_encode(['success' => true, 'users' => $users]);
}

function markAsRead($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $sender_id = $input['sender_id'] ?? '';

    if (empty($sender_id)) {
        echo json_encode(['success' => false, 'error' => 'Sender ID required']);
        return;
    }

    // Mark messages as read
    $stmt = $connection->prepare("
        UPDATE tbl_chat
        SET is_read = 1, read_at = NOW()
        WHERE sender_id = ? AND recipient_id = ? AND is_read = 0
    ");
    $stmt->bind_param("ii", $sender_id, $user_id);

    if ($stmt->execute()) {
        // Also mark notifications as read
        $stmt2 = $connection->prepare("
            UPDATE tbl_chat_notifications cn
            JOIN tbl_chat c ON cn.message_id = c.id
            SET cn.is_read = 1, cn.read_at = NOW()
            WHERE c.sender_id = ? AND cn.user_id = ? AND cn.is_read = 0
        ");
        $stmt2->bind_param("ii", $sender_id, $user_id);
        $stmt2->execute();

        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to mark as read']);
    }
}

function getUnreadCount($connection, $user_id) {
    $stmt = $connection->prepare("
        SELECT COUNT(*) as unread_count 
        FROM tbl_chat 
        WHERE recipient_id = ? AND is_read = 0 AND is_deleted = 0
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    echo json_encode(['success' => true, 'unread_count' => $row['unread_count']]);
}

// Helper functions
function updateConversation($connection, $user1, $user2, $message_id) {
    $stmt = $connection->prepare("
        INSERT INTO tbl_conversations (participant_1, participant_2, last_message_id) 
        VALUES (LEAST(?, ?), GREATEST(?, ?), ?) 
        ON DUPLICATE KEY UPDATE 
        last_message_id = VALUES(last_message_id), 
        last_activity = NOW()
    ");
    $stmt->bind_param("iiiii", $user1, $user2, $user1, $user2, $message_id);
    $stmt->execute();
}

function createNotification($connection, $user_id, $message_id) {
    $stmt = $connection->prepare("INSERT INTO tbl_chat_notifications (user_id, message_id) VALUES (?, ?)");
    $stmt->bind_param("ii", $user_id, $message_id);
    $stmt->execute();
}

function uploadFile($connection, $user_id) {
    if (!isset($_FILES['file']) || !isset($_POST['recipient_id'])) {
        echo json_encode(['success' => false, 'error' => 'Missing file or recipient']);
        return;
    }

    $recipient_id = $_POST['recipient_id'];
    $file = $_FILES['file'];

    // Validate file
    $maxSize = 10 * 1024 * 1024; // 10MB
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    if ($file['size'] > $maxSize) {
        echo json_encode(['success' => false, 'error' => 'File too large (max 10MB)']);
        return;
    }

    if (!in_array($file['type'], $allowedTypes)) {
        echo json_encode(['success' => false, 'error' => 'File type not allowed']);
        return;
    }

    // Create upload directory if it doesn't exist
    $uploadDir = 'uploads/chat/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $storedName = uniqid() . '_' . time() . '.' . $extension;
    $filePath = $uploadDir . $storedName;

    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Insert message with file
        $message = 'File: ' . $file['name'];
        $messageType = strpos($file['type'], 'image/') === 0 ? 'image' : 'file';

        $stmt = $connection->prepare("INSERT INTO tbl_chat (sender_id, recipient_id, message, message_type, file_name, file_path, file_size) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("iissssi", $user_id, $recipient_id, $message, $messageType, $file['name'], $filePath, $file['size']);

        if ($stmt->execute()) {
            $message_id = $connection->insert_id;

            // Insert file details
            $stmt2 = $connection->prepare("INSERT INTO tbl_chat_files (message_id, original_name, stored_name, file_path, file_size, file_type, mime_type) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt2->bind_param("isssiss", $message_id, $file['name'], $storedName, $filePath, $file['size'], $extension, $file['type']);
            $stmt2->execute();

            // Update conversation and create notification
            updateConversation($connection, $user_id, $recipient_id, $message_id);
            createNotification($connection, $recipient_id, $message_id);

            echo json_encode(['success' => true, 'message_id' => $message_id, 'file_path' => $filePath]);
        } else {
            unlink($filePath); // Delete uploaded file if database insert fails
            echo json_encode(['success' => false, 'error' => 'Failed to save file message']);
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to upload file']);
    }
}

function getUserSettings($connection, $user_id) {
    $stmt = $connection->prepare("SELECT * FROM tbl_chat_settings WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $settings = $result->fetch_assoc();

    if (!$settings) {
        // Create default settings
        $stmt = $connection->prepare("INSERT INTO tbl_chat_settings (user_id) VALUES (?)");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();

        $settings = [
            'sound_notifications' => 1,
            'desktop_notifications' => 1,
            'show_online_status' => 1,
            'auto_away_minutes' => 15,
            'theme' => 'light'
        ];
    }

    echo json_encode(['success' => true, 'settings' => $settings]);
}

function updateSettings($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);

    $stmt = $connection->prepare("
        UPDATE tbl_chat_settings
        SET sound_notifications = ?, desktop_notifications = ?, show_online_status = ?, auto_away_minutes = ?, theme = ?
        WHERE user_id = ?
    ");
    $stmt->bind_param("iiiisi",
        $input['sound_notifications'],
        $input['desktop_notifications'],
        $input['show_online_status'],
        $input['auto_away_minutes'],
        $input['theme'],
        $user_id
    );

    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to update settings']);
    }
}

function markNotificationAsRead($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $message_id = $input['message_id'] ?? '';

    if (empty($message_id)) {
        echo json_encode(['success' => false, 'error' => 'Message ID required']);
        return;
    }

    $stmt = $connection->prepare("
        UPDATE tbl_chat_notifications
        SET is_read = 1, read_at = NOW()
        WHERE user_id = ? AND message_id = ?
    ");
    $stmt->bind_param("ii", $user_id, $message_id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to mark notification as read']);
    }
}

function editMessage($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true, 512, JSON_UNESCAPED_UNICODE);
    $message_id = $input['message_id'] ?? '';
    $new_message = trim($input['message'] ?? '');

    // Ensure proper UTF-8 encoding for emojis
    $new_message = mb_convert_encoding($new_message, 'UTF-8', 'UTF-8');

    if (empty($message_id) || empty($new_message)) {
        echo json_encode(['success' => false, 'error' => 'Message ID and new message required']);
        return;
    }

    // Check if message exists and belongs to user
    $stmt = $connection->prepare("
        SELECT id, message, timestamp, message_type
        FROM tbl_chat
        WHERE id = ? AND sender_id = ? AND is_deleted = 0
    ");
    $stmt->bind_param("ii", $message_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $message = $result->fetch_assoc();

    if (!$message) {
        echo json_encode(['success' => false, 'error' => 'Message not found or access denied']);
        return;
    }

    // Check if message is within edit time limit (10 minutes)
    $message_time = strtotime($message['timestamp']);
    $current_time = time();
    $time_diff = $current_time - $message_time;

    if ($time_diff > 600) { // 10 minutes = 600 seconds
        echo json_encode(['success' => false, 'error' => 'Message can only be edited within 10 minutes']);
        return;
    }

    // Only allow editing text messages
    if ($message['message_type'] !== 'text') {
        echo json_encode(['success' => false, 'error' => 'Only text messages can be edited']);
        return;
    }

    // Update message
    $stmt = $connection->prepare("
        UPDATE tbl_chat
        SET message = ?, is_edited = 1, edited_at = NOW(), original_message = ?
        WHERE id = ? AND sender_id = ?
    ");
    $stmt->bind_param("ssii", $new_message, $message['message'], $message_id, $user_id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'edited_at' => date('Y-m-d H:i:s')]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to edit message']);
    }
}

function deleteMessage($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $message_id = $input['message_id'] ?? '';
    $delete_for = $input['delete_for'] ?? 'sender'; // 'sender' or 'both'

    if (empty($message_id)) {
        echo json_encode(['success' => false, 'error' => 'Message ID required']);
        return;
    }

    // Check if message exists and belongs to user
    $stmt = $connection->prepare("
        SELECT id, timestamp, message_type
        FROM tbl_chat
        WHERE id = ? AND sender_id = ? AND deleted_for != 'both'
    ");
    $stmt->bind_param("ii", $message_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $message = $result->fetch_assoc();

    if (!$message) {
        echo json_encode(['success' => false, 'error' => 'Message not found or access denied']);
        return;
    }

    // Check if message is within delete time limit (10 minutes for "delete for everyone")
    $message_time = strtotime($message['timestamp']);
    $current_time = time();
    $time_diff = $current_time - $message_time;

    if ($delete_for === 'both' && $time_diff > 600) { // 10 minutes = 600 seconds
        echo json_encode(['success' => false, 'error' => 'Message can only be deleted for everyone within 10 minutes']);
        return;
    }

    // Update message based on delete type
    if ($delete_for === 'both') {
        // Delete for everyone
        $stmt = $connection->prepare("
            UPDATE tbl_chat
            SET is_deleted = 1, deleted_for = 'both', message = 'This message was deleted'
            WHERE id = ? AND sender_id = ?
        ");
    } else {
        // Delete for sender only
        $stmt = $connection->prepare("
            UPDATE tbl_chat
            SET deleted_for = 'sender'
            WHERE id = ? AND sender_id = ?
        ");
    }

    $stmt->bind_param("ii", $message_id, $user_id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'delete_for' => $delete_for]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to delete message']);
    }
}
?>
