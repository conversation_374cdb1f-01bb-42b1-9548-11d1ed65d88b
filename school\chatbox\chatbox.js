// Enhanced Chatbox JavaScript
class Chatbox {
    constructor() {
        this.currentRecipientId = null;
        this.lastMessageId = 0;
        this.lastPollTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
        this.pollInterval = null;
        this.heartbeatInterval = null;
        this.searchTimeout = null;
        this.loadingMessages = null;
        this.isMinimized = false;
        this.selectedFile = null;
        this.notificationSound = document.getElementById('notification-sound');
        this.processedNotifications = new Set(); // Track processed notifications
        this.settings = {
            sound_notifications: true,
            desktop_notifications: true,
            show_online_status: true
        };

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUserSettings();
        this.updateUserStatus('online');
        this.startHeartbeat();
        this.startPolling();
        this.loadConversations();
        this.loadUsers();
        this.requestNotificationPermission();
        this.initializeChatWindow();
    }

    initializeChatWindow() {
        // Ensure chat window is properly hidden initially
        const chatWindow = document.getElementById('chat-window');
        const messageInputArea = document.getElementById('message-input-area');
        const chatHeader = document.getElementById('chat-header');

        if (chatWindow) {
            chatWindow.style.display = 'none';
        }
        if (messageInputArea) {
            messageInputArea.style.display = 'none';
        }
        if (chatHeader) {
            chatHeader.style.display = 'none';
        }
    }
    
    bindEvents() {
        // Toggle chat panel
        document.getElementById('chat-toggle').addEventListener('click', () => {
            this.toggleChatPanel();
        });
        
        // Close chat
        document.getElementById('close-chat').addEventListener('click', () => {
            this.closeChatPanel();
        });
        
        // Minimize chat
        document.getElementById('minimize-chat').addEventListener('click', () => {
            this.minimizeChat();
        });
        
        // Search users
        document.getElementById('search-btn').addEventListener('click', () => {
            this.searchUsers();
        });

        // Real-time search as user types
        document.getElementById('user-search').addEventListener('input', (e) => {
            const searchValue = e.target.value.trim();
            const clearBtn = document.getElementById('clear-search');

            // Show/hide clear button
            if (searchValue.length > 0) {
                clearBtn.style.display = 'block';
            } else {
                clearBtn.style.display = 'none';
            }

            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.searchUsers();
            }, 300); // Debounce search by 300ms
        });

        // Clear search functionality
        document.getElementById('clear-search').addEventListener('click', () => {
            document.getElementById('user-search').value = '';
            document.getElementById('clear-search').style.display = 'none';
            this.clearSearch();
        });

        document.getElementById('user-search').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                clearTimeout(this.searchTimeout);
                this.searchUsers();
            }
        });
        
        // Tab switching
        document.getElementById('users-tab').addEventListener('click', () => {
            const searchValue = document.getElementById('user-search').value.trim();
            if (searchValue.length === 0) {
                // Show helpful message when no search term
                const container = document.getElementById('users-list');
                container.innerHTML = `
                    <div class="text-center p-3 text-muted">
                        <i class="fa fa-users fa-2x mb-2"></i>
                        <p class="small">Use the search bar above to find users to chat with.</p>
                        <p class="small">Try searching by name, email, or position.</p>
                    </div>
                `;
            } else {
                this.loadUsers();
            }
        });
        
        // Message form
        document.getElementById('message-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });
        
        // Message input
        document.getElementById('message-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Chat window controls
        document.getElementById('back-to-list').addEventListener('click', () => {
            this.showChatPanel();
        });
        
        document.getElementById('close-chat-window').addEventListener('click', () => {
            this.closeChatWindow();
        });
        
        // Handle page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.updateUserStatus('away');
            } else {
                this.updateUserStatus('online');

                // Clear notifications for current recipient when user returns to tab
                if (this.currentRecipientId && window.chatNotificationHandler) {
                    setTimeout(() => {
                        window.chatNotificationHandler.clearNotificationsForSender(this.currentRecipientId);
                    }, 500);
                }
            }
        });
        
        // Handle page unload
        window.addEventListener('beforeunload', () => {
            this.updateUserStatus('offline');
        });

        // Add drag and drop functionality
        this.setupDragAndDrop();
    }
    
    toggleChatPanel() {
        const panel = document.getElementById('chat-panel');
        const isOpen = panel.classList.contains('open');
        
        if (isOpen) {
            this.closeChatPanel();
        } else {
            this.openChatPanel();
        }
    }
    
    openChatPanel() {
        document.getElementById('chat-panel').classList.add('open');
        this.loadConversations();
    }
    
    closeChatPanel() {
        document.getElementById('chat-panel').classList.remove('open');
        document.getElementById('chat-window').classList.remove('open');
    }
    
    minimizeChat() {
        this.isMinimized = !this.isMinimized;
        const panel = document.getElementById('chat-panel');
        const button = document.getElementById('minimize-chat');
        
        if (this.isMinimized) {
            panel.style.height = '60px';
            button.innerHTML = '<i class="fa fa-plus"></i>';
        } else {
            panel.style.height = '100vh';
            button.innerHTML = '<i class="fa fa-minus"></i>';
        }
    }
    
    showChatWindow(userId, userName, userImage, userStatus) {
        console.log('Opening chat window for:', userName, 'ID:', userId);

        this.currentRecipientId = userId;

        // Ensure the current recipient ID is set
        const recipientInput = document.getElementById('current-recipient-id');
        if (recipientInput) {
            recipientInput.value = userId;
        } else {
            console.warn('current-recipient-id input not found');
        }

        // Update chat window header
        const chatUserName = document.getElementById('chat-user-name');
        const chatUserStatus = document.getElementById('chat-user-status');
        const chatUserAvatar = document.getElementById('chat-user-avatar');

        if (chatUserName) chatUserName.textContent = userName;
        if (chatUserStatus) chatUserStatus.textContent = this.formatStatus(userStatus);
        if (chatUserAvatar) {
            chatUserAvatar.src = userImage ? `images/profile/thumbs/${userImage}` : 'images/profile/thumbs/profile.png';
        }

        // Hide chat panel and show chat window
        const chatPanel = document.getElementById('chat-panel');
        const chatWindow = document.getElementById('chat-window');
        const messageInputArea = document.getElementById('message-input-area');
        const chatHeader = document.getElementById('chat-header');

        console.log('Elements found:', {
            chatPanel: !!chatPanel,
            chatWindow: !!chatWindow,
            messageInputArea: !!messageInputArea,
            chatHeader: !!chatHeader
        });

        if (chatPanel) chatPanel.classList.remove('open');
        if (chatWindow) {
            chatWindow.classList.add('open');
            chatWindow.style.display = 'flex'; // Ensure it's visible
            console.log('Chat window opened');
        } else {
            console.error('Chat window element not found!');
        }

        // Show message input area and header
        if (messageInputArea) {
            messageInputArea.style.display = 'block';
            console.log('Message input area shown');
        } else {
            console.error('Message input area not found!');
        }

        if (chatHeader) {
            chatHeader.style.display = 'block';
            console.log('Chat header shown');
        } else {
            console.error('Chat header not found!');
        }

        // Clear the "no conversation" message
        const noConversationMessage = document.getElementById('noConversationMessage');
        if (noConversationMessage) {
            noConversationMessage.style.display = 'none';
        }

        // Clear notifications for this sender immediately
        this.clearNotificationsForSender(userId);

        // Also clear via notification handler
        if (window.chatNotificationHandler) {
            window.chatNotificationHandler.clearNotificationsForSender(userId);
        }

        // Load messages for this user
        this.loadMessages(userId);

        // Mark messages as read
        this.markAsRead(userId);

        // Focus on message input
        setTimeout(() => {
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.focus();
                console.log('Message input focused');
            } else {
                console.error('Message input not found!');
            }
        }, 300);
    }
    
    showChatPanel() {
        document.getElementById('chat-window').classList.remove('open');
        document.getElementById('chat-panel').classList.add('open');
    }
    
    closeChatWindow() {
        document.getElementById('chat-window').classList.remove('open');
        this.currentRecipientId = null;
    }
    
    async loadConversations() {
        try {
            const response = await fetch('chatbox/chat_api.php?action=get_conversations');
            const data = await response.json();
            
            if (data.success) {
                this.renderConversations(data.conversations);
                this.updateUnreadCount();
            }
        } catch (error) {
            console.error('Error loading conversations:', error);
        }
    }
    
    renderConversations(conversations) {
        const container = document.getElementById('conversations-list');
        
        if (conversations.length === 0) {
            container.innerHTML = `
                <div class="text-center p-3 text-muted">
                    <i class="fa fa-comments fa-2x mb-2"></i>
                    <p class="small">No conversations yet.<br>Search for users to start chatting!</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = conversations.map(conv => `
            <div class="chat-list-item" onclick="chatbox.showChatWindow(${conv.user_id}, '${conv.name}', '${conv.image}', '${conv.status}')">
                <img src="images/profile/thumbs/${conv.image}" alt="${conv.name}" class="chat-avatar">
                <div class="chat-item-content">
                    <div class="chat-item-name">
                        ${conv.name}
                        <span class="status-indicator status-${conv.status}"></span>
                    </div>
                    <div class="chat-item-message">${conv.last_message || 'No messages yet'}</div>
                </div>
                <div class="chat-item-meta">
                    <div class="chat-item-time">${this.formatTime(conv.last_message_time)}</div>
                    ${conv.unread_count > 0 ? `<span class="chat-item-badge">${conv.unread_count}</span>` : ''}
                </div>
            </div>
        `).join('');
    }
    
    async loadUsers() {
        const search = document.getElementById('user-search').value.trim();
        const container = document.getElementById('users-list');

        // Show loading state
        container.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="small mt-2 mb-0">Searching users...</p>
            </div>
        `;

        try {
            const response = await fetch(`chatbox/chat_api.php?action=get_users&search=${encodeURIComponent(search)}`);
            const data = await response.json();

            if (data.success) {
                this.renderUsers(data.users, search);
            } else {
                container.innerHTML = `
                    <div class="text-center p-3 text-muted">
                        <i class="fa fa-exclamation-triangle fa-2x mb-2"></i>
                        <p class="small">Error loading users. Please try again.</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading users:', error);
            container.innerHTML = `
                <div class="text-center p-3 text-muted">
                    <i class="fa fa-exclamation-triangle fa-2x mb-2"></i>
                    <p class="small">Network error. Please check your connection.</p>
                </div>
            `;
        }
    }
    
    renderUsers(users, searchTerm = '') {
        const container = document.getElementById('users-list');

        if (users.length === 0) {
            const message = searchTerm.length > 0
                ? `No users found for "${searchTerm}"`
                : 'No users found.';

            container.innerHTML = `
                <div class="text-center p-3 text-muted">
                    <i class="fa fa-users fa-2x mb-2"></i>
                    <p class="small">${message}</p>
                    ${searchTerm.length > 0 ? '<p class="small">Try a different search term.</p>' : ''}
                </div>
            `;
            return;
        }

        // Highlight search terms in user names
        const highlightText = (text, term) => {
            if (!term) return text;
            const regex = new RegExp(`(${term})`, 'gi');
            return text.replace(regex, '<mark>$1</mark>');
        };

        container.innerHTML = users.map(user => `
            <div class="chat-list-item" onclick="chatbox.showChatWindow(${user.id}, '${user.name.replace(/'/g, "\\'")}', '${user.image}', '${user.status}')">
                <img src="images/profile/thumbs/${user.image}" alt="${user.name}" class="chat-avatar">
                <div class="chat-item-content">
                    <div class="chat-item-name">
                        ${highlightText(user.name, searchTerm)}
                        <span class="status-indicator status-${user.status}"></span>
                    </div>
                    <div class="chat-item-message">${highlightText(user.position || '', searchTerm)}</div>
                </div>
                <div class="chat-item-meta">
                    <div class="chat-item-time">${this.formatStatus(user.status)}</div>
                </div>
            </div>
        `).join('');

        // Show result count
        if (searchTerm.length > 0) {
            const countEl = document.createElement('div');
            countEl.className = 'text-center p-2 bg-light border-bottom';
            countEl.innerHTML = `<small class="text-muted">${users.length} user${users.length !== 1 ? 's' : ''} found</small>`;
            container.insertBefore(countEl, container.firstChild);
        }
    }
    
    searchUsers() {
        const searchTerm = document.getElementById('user-search').value.trim();

        // If there's a search term, automatically switch to users tab
        if (searchTerm.length > 0) {
            const usersTab = document.getElementById('users-tab');
            const usersPane = document.getElementById('users-pane');
            const conversationsTab = document.getElementById('conversations-tab');
            const conversationsPane = document.getElementById('conversations-pane');

            // Switch tabs
            conversationsTab.classList.remove('active');
            conversationsPane.classList.remove('show', 'active');
            usersTab.classList.add('active');
            usersPane.classList.add('show', 'active');
        }

        this.loadUsers();
    }

    clearSearch() {
        // Switch back to conversations tab
        const conversationsTab = document.getElementById('conversations-tab');
        const conversationsPane = document.getElementById('conversations-pane');
        const usersTab = document.getElementById('users-tab');
        const usersPane = document.getElementById('users-pane');

        usersTab.classList.remove('active');
        usersPane.classList.remove('show', 'active');
        conversationsTab.classList.add('active');
        conversationsPane.classList.add('show', 'active');

        // Reset users list to default state
        const container = document.getElementById('users-list');
        container.innerHTML = `
            <div class="text-center p-3 text-muted">
                <i class="fa fa-users fa-2x mb-2"></i>
                <p class="small">Use the search bar to find users to chat with.</p>
            </div>
        `;

        // Reload conversations
        this.loadConversations();
    }
    
    async loadMessages(recipientId, forceReload = false) {
        // Prevent loading if already loading for the same recipient
        if (this.loadingMessages && this.loadingMessages === recipientId && !forceReload) {
            return;
        }

        this.loadingMessages = recipientId;

        try {
            const response = await fetch(`chatbox/chat_api.php?action=get_messages&recipient_id=${recipientId}&last_message_id=0`);
            const data = await response.json();

            if (data.success) {
                // Only render if this is still the current recipient
                if (this.currentRecipientId === recipientId) {
                    this.renderMessages(data.messages);
                    if (data.messages.length > 0) {
                        const maxId = Math.max(...data.messages.map(m => m.id));
                        this.lastMessageId = maxId;
                        console.log('Set lastMessageId to:', maxId);
                    } else {
                        console.log('No messages loaded, keeping lastMessageId:', this.lastMessageId);
                    }

                    // Ensure message input area is visible
                    const messageInputArea = document.getElementById('message-input-area');
                    if (messageInputArea) {
                        messageInputArea.style.display = 'block';
                    }
                }
            }
        } catch (error) {
            console.error('Error loading messages:', error);
            // Still show input area even if loading fails
            if (this.currentRecipientId === recipientId) {
                const messageInputArea = document.getElementById('message-input-area');
                if (messageInputArea) {
                    messageInputArea.style.display = 'block';
                }
            }
        } finally {
            this.loadingMessages = null;
        }
    }
    
    renderMessages(messages) {
        const container = document.getElementById('chat-messages');
        const messageInputArea = document.getElementById('message-input-area');

        if (messages.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fa fa-comment-o fa-3x mb-3"></i>
                    <p>Start your conversation!</p>
                </div>
            `;
        } else {
            container.innerHTML = messages.map(msg => this.renderMessage(msg)).join('');
            container.scrollTop = container.scrollHeight;
        }

        // Always ensure message input area is visible when we have an active conversation
        if (messageInputArea && this.currentRecipientId) {
            messageInputArea.style.display = 'block';
        }
    }

    appendNewMessage(message) {
        const container = document.getElementById('chat-messages');

        // Check if message already exists to prevent duplicates
        const existingMessage = container.querySelector(`[data-message-id="${message.id}"]`);
        if (existingMessage) {
            return; // Message already displayed
        }

        // Remove "start conversation" message if it exists
        const emptyMessage = container.querySelector('.text-center.text-muted');
        if (emptyMessage) {
            emptyMessage.remove();
        }

        // Create new message element
        const messageElement = document.createElement('div');
        messageElement.innerHTML = this.renderMessage(message);
        messageElement.setAttribute('data-message-id', message.id);

        // Add with animation
        messageElement.style.opacity = '0';
        messageElement.style.transform = 'translateY(20px)';
        container.appendChild(messageElement);

        // Animate in
        setTimeout(() => {
            messageElement.style.transition = 'all 0.3s ease';
            messageElement.style.opacity = '1';
            messageElement.style.transform = 'translateY(0)';
        }, 10);

        // Scroll to bottom
        container.scrollTop = container.scrollHeight;

        // Update last message ID
        if (message.id > this.lastMessageId) {
            this.lastMessageId = message.id;
            console.log('Updated lastMessageId to:', this.lastMessageId);
        }
    }
    
    renderMessage(message) {
        const isOwn = message.sender_id == document.querySelector('input[name="user_id"]')?.value || message.sender_id == window.currentUserId;
        const messageClass = isOwn ? 'message own' : 'message';

        let content = '';
        if (message.message_type === 'image') {
            // For images, show thumbnail and allow click to view full size
            const imageUrl = message.file_path ? message.file_path : `chatbox/chat_download.php?message_id=${message.id}`;
            content = `
                <div class="message-text">${this.escapeHtml(message.message)}</div>
                <div class="message-image-container">
                    <img src="${imageUrl}" alt="${message.file_name}" class="message-image"
                         onclick="window.open('${imageUrl}', '_blank')"
                         onerror="this.src='chatbox/chat_download.php?message_id=${message.id}'">
                    <div class="image-overlay">
                        <i class="fa fa-search-plus"></i>
                    </div>
                </div>
                <div class="file-info-small">
                    <span class="file-name">${message.file_name}</span>
                    <span class="file-size">${this.formatFileSize(message.file_size || 0)}</span>
                </div>
            `;
        } else if (message.message_type === 'file') {
            content = `
                <div class="message-text">${this.escapeHtml(message.message)}</div>
                <div class="message-file" onclick="window.open('chatbox/chat_download.php?message_id=${message.id}', '_blank')">
                    <i class="fa fa-${this.getFileIcon(message.file_type)}"></i>
                    <div class="file-info">
                        <div class="file-name">${message.file_name}</div>
                        <div class="file-size">${this.formatFileSize(message.file_size || 0)}</div>
                    </div>
                </div>
            `;
        } else {
            content = `<div class="message-text">${this.escapeHtml(message.message)}</div>`;
        }

        return `
            <div class="${messageClass}" data-message-id="${message.id}">
                <img src="images/profile/thumbs/${message.sender_image}" alt="${message.sender_name}" class="message-avatar">
                <div class="message-content">
                    ${content}
                    <div class="message-time">${this.formatTime(message.timestamp)}</div>
                </div>
            </div>
        `;
    }
    
    async sendMessage() {
        const messageInput = document.getElementById('message-input');
        const message = messageInput.value.trim();
        
        if (!message && !this.selectedFile) return;
        if (!this.currentRecipientId) return;
        
        if (this.selectedFile) {
            await this.sendFile();
        } else {
            await this.sendTextMessage(message);
        }
        
        messageInput.value = '';
        this.clearFileSelection();
    }
    
    async sendTextMessage(message) {
        try {
            const response = await fetch('chatbox/chat_api.php?action=send_message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: this.currentRecipientId,
                    message: message,
                    message_type: 'text'
                })
            });

            const data = await response.json();
            if (data.success) {
                // Create a temporary message object to display immediately
                const tempMessage = {
                    id: data.message_id,
                    sender_id: window.currentUserId,
                    recipient_id: this.currentRecipientId,
                    message: message,
                    message_type: 'text',
                    timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
                    sender_name: 'You',
                    sender_image: 'profile.png'
                };

                // Add message immediately without waiting for polling
                this.appendNewMessage(tempMessage);

                // Update last message ID to prevent showing this as notification
                if (data.message_id > this.lastMessageId) {
                    this.lastMessageId = data.message_id;
                    console.log('Updated lastMessageId after sending to:', this.lastMessageId);
                }

                // Update conversations list (but don't reload messages)
                this.loadConversations();
            }
        } catch (error) {
            console.error('Error sending message:', error);
        }
    }
    
    async sendFile() {
        const formData = new FormData();
        formData.append('file', this.selectedFile);
        formData.append('recipient_id', this.currentRecipientId);
        formData.append('action', 'upload_file');

        try {
            const response = await fetch('chatbox/chat_api.php', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                // For files, we need to reload to get the proper file path
                // But we can optimize by only loading new messages
                setTimeout(() => {
                    this.loadMessages(this.currentRecipientId, true);
                }, 500);

                this.loadConversations();
            }
        } catch (error) {
            console.error('Error uploading file:', error);
        }
    }
    
    handleFileSelect(input) {
        const file = input.files[0];
        if (file) {
            if (this.validateFile(file)) {
                this.selectedFile = file;
                this.showFilePreview(file);
            }
        }
    }

    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        if (file.size > maxSize) {
            alert('File is too large. Maximum size is 10MB.');
            return false;
        }

        if (!allowedTypes.includes(file.type)) {
            alert('File type not supported. Please select an image, PDF, or document file.');
            return false;
        }

        return true;
    }

    showFilePreview(file) {
        const preview = document.getElementById('file-preview');
        const fileName = document.getElementById('file-name');

        fileName.innerHTML = `
            <i class="fa fa-${this.getFileIcon(file.type)}"></i>
            <span>${file.name}</span>
            <small class="text-muted">(${this.formatFileSize(file.size)})</small>
        `;

        // Show image preview for image files
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                fileName.innerHTML += `
                    <div class="mt-2">
                        <img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 100px; border-radius: 4px;">
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }

        preview.style.display = 'block';
    }

    getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return 'image';
        if (mimeType === 'application/pdf') return 'file-pdf-o';
        if (mimeType.includes('word')) return 'file-word-o';
        if (mimeType.includes('excel') || mimeType.includes('sheet')) return 'file-excel-o';
        return 'file-o';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    setupDragAndDrop() {
        const chatWindow = document.getElementById('chat-window');
        const inputArea = document.querySelector('.chat-input-area');

        if (!chatWindow || !inputArea) return;

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            chatWindow.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        // Highlight drop area
        ['dragenter', 'dragover'].forEach(eventName => {
            chatWindow.addEventListener(eventName, () => this.highlight(inputArea), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            chatWindow.addEventListener(eventName, () => this.unhighlight(inputArea), false);
        });

        // Handle dropped files
        chatWindow.addEventListener('drop', (e) => this.handleDrop(e), false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight(element) {
        element.classList.add('drag-over');
    }

    unhighlight(element) {
        element.classList.remove('drag-over');
    }

    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const file = files[0];
            if (this.validateFile(file)) {
                this.selectedFile = file;
                this.showFilePreview(file);
            }
        }
    }
    
    clearFileSelection() {
        this.selectedFile = null;
        document.getElementById('file-input').value = '';
        document.getElementById('file-preview').style.display = 'none';
    }
    
    async markAsRead(senderId) {
        try {
            const response = await fetch('chatbox/chat_api.php?action=mark_as_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sender_id: senderId
                })
            });

            if (response.ok) {
                // Also clear any notifications for this sender
                this.clearNotificationsForSender(senderId);
            }
        } catch (error) {
            console.error('Error marking as read:', error);
        }
    }
    
    async updateUnreadCount() {
        try {
            const response = await fetch('chatbox/chat_api.php?action=get_unread_count');
            const data = await response.json();
            
            if (data.success) {
                const count = data.unread_count;
                const badge = document.getElementById('chat-badge');
                const totalBadge = document.getElementById('total-unread-badge');
                
                if (count > 0) {
                    badge.textContent = count;
                    badge.style.display = 'flex';
                    totalBadge.textContent = count;
                    totalBadge.style.display = 'inline';
                } else {
                    badge.style.display = 'none';
                    totalBadge.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Error updating unread count:', error);
        }
    }
    
    async updateUserStatus(status) {
        try {
            await fetch('chatbox/chat_api.php?action=update_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    status: status
                })
            });
            
            // Update status indicator
            const indicator = document.getElementById('online-status-indicator');
            const dot = indicator.querySelector('.status-dot');
            const text = indicator.querySelector('.status-text');
            
            dot.className = `status-dot status-${status}`;
            text.textContent = this.formatStatus(status);
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }
    
    startPolling() {
        this.pollInterval = setInterval(() => {
            // Only poll if chat panel is open or we have an active conversation
            const chatPanel = document.getElementById('chat-panel');
            const chatWindow = document.getElementById('chat-window');

            if ((chatPanel && chatPanel.classList.contains('open')) ||
                (chatWindow && chatWindow.classList.contains('open')) ||
                !document.hidden) {
                this.pollForUpdates();
            }
        }, 5000); // Poll every 5 seconds
    }
    
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            fetch('chatbox/chat_realtime.php?action=heartbeat');
        }, 30000); // Heartbeat every 30 seconds
    }
    
    async pollForUpdates() {
        try {
            // Use last message ID instead of timestamp for more reliable tracking
            const lastMessageParam = this.lastMessageId > 0 ? `&last_message_id=${this.lastMessageId}` : '';
            const response = await fetch(`chatbox/chat_realtime.php?action=poll&last_check=${encodeURIComponent(this.lastPollTime)}${lastMessageParam}`);
            const data = await response.json();

            if (data.success) {
                // Update poll time before processing to prevent race conditions
                const newPollTime = data.timestamp;

                // Handle new messages
                if (data.new_messages.length > 0) {
                    console.log('Received new messages:', data.new_messages.length, 'IDs:', data.new_messages.map(m => m.id));

                    // Filter out messages we've already seen
                    const trulyNewMessages = data.new_messages.filter(msg => msg.id > this.lastMessageId);

                    if (trulyNewMessages.length > 0) {
                        console.log('Processing truly new messages:', trulyNewMessages.length);
                        this.handleNewMessages(trulyNewMessages);

                        // Update last message ID to the highest ID we've seen
                        this.lastMessageId = Math.max(...trulyNewMessages.map(m => m.id));
                    }
                }

                // Handle status updates
                if (data.status_updates.length > 0) {
                    this.handleStatusUpdates(data.status_updates);
                }

                // Handle notifications
                if (data.notifications.length > 0) {
                    this.handleNotifications(data.notifications);
                }

                // Update poll time after processing
                this.lastPollTime = newPollTime;
            }
        } catch (error) {
            console.error('Error polling for updates:', error);
        }
    }
    
    handleNewMessages(messages) {
        if (messages.length === 0) return;

        console.log('Processing messages:', messages.map(m => `ID:${m.id} from:${m.sender_id}`));

        messages.forEach(message => {
            console.log('Processing message:', message.id, 'from:', message.sender_id, 'current recipient:', this.currentRecipientId);

            if (this.currentRecipientId == message.sender_id) {
                // Only append new messages instead of reloading all
                this.appendNewMessage(message);
                this.markAsRead(message.sender_id);

                // Still mark notification as read for current chat
                if (window.chatNotificationHandler && message.id) {
                    window.chatNotificationHandler.markNotificationAsRead(message.id);
                }

                // Clear any existing notifications for this sender since chat is open
                if (window.chatNotificationHandler) {
                    window.chatNotificationHandler.clearNotificationsForSender(message.sender_id);
                }
            } else {
                // Show notification using notification handler
                if (window.chatNotificationHandler) {
                    // Check if we already showed notification for this message
                    if (!window.chatNotificationHandler.hasShownNotification(message.id)) {
                        console.log('Showing notification for message:', message.id, 'from:', message.sender_id);

                        // Customize message text based on type
                        let notificationMessage = message.message;
                        if (message.message_type === 'image') {
                            notificationMessage = '📷 Sent an image';
                        } else if (message.message_type === 'file') {
                            notificationMessage = '📎 Sent a file: ' + (message.file_name || 'attachment');
                        }

                        window.chatNotificationHandler.showNotification({
                            title: 'New Message',
                            message: notificationMessage,
                            senderId: message.sender_id,
                            senderName: message.sender_name,
                            senderImage: message.sender_image,
                            messageId: message.id,
                            timestamp: message.timestamp,
                            type: message.message_type || 'text'
                        });
                    } else {
                        console.log('Notification already shown for message:', message.id);
                    }
                }
            }
        });

        // Update conversations and unread count
        this.loadConversations();
        this.updateUnreadCount();
    }
    
    handleStatusUpdates(updates) {
        // Update user status in conversations and user lists
        this.loadConversations();
        if (document.getElementById('users-tab').classList.contains('active')) {
            this.loadUsers();
        }
    }
    
    handleNotifications(notifications) {
        notifications.forEach(notification => {
            this.showNotification(notification.sender_name, notification.message);
        });
    }
    
    showNotification(title, message) {
        // Delegate to notification handler
        if (window.chatNotificationHandler) {
            window.chatNotificationHandler.showNotification({
                title: title,
                message: message,
                type: 'general'
            });
        }
    }

    playNotificationSound() {
        // Delegate to notification handler
        if (window.chatNotificationHandler) {
            window.chatNotificationHandler.playNotificationSound();
        }
    }

    requestNotificationPermission() {
        // Delegate to notification handler
        if (window.chatNotificationHandler) {
            window.chatNotificationHandler.requestPermission();
        }
    }
    
    async loadUserSettings() {
        try {
            const response = await fetch('chatbox/chat_api.php?action=get_user_settings');
            const data = await response.json();
            
            if (data.success) {
                this.settings = data.settings;
            }
        } catch (error) {
            console.error('Error loading user settings:', error);
        }
    }
    
    formatTime(timestamp) {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return Math.floor(diff / 60000) + 'm';
        if (diff < 86400000) return Math.floor(diff / 3600000) + 'h';
        return date.toLocaleDateString();
    }
    
    formatStatus(status) {
        const statusMap = {
            'online': 'Online',
            'away': 'Away',
            'busy': 'Busy',
            'offline': 'Offline'
        };
        return statusMap[status] || 'Unknown';
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    clearNotificationsForSender(senderId) {
        // Clear in-app notifications for this sender
        if (window.chatNotificationHandler) {
            const notifications = document.querySelectorAll(`[data-sender-id="${senderId}"]`);
            notifications.forEach(notification => {
                window.chatNotificationHandler.removeNotification(notification);
            });
        }

        // Clear desktop notifications for this sender
        if (window.chatNotificationHandler && window.chatNotificationHandler.activeNotifications) {
            const tag = `chat-${senderId}`;
            const notification = window.chatNotificationHandler.activeNotifications.get(tag);
            if (notification) {
                notification.close();
                window.chatNotificationHandler.activeNotifications.delete(tag);
            }
        }
    }
}

// Initialize chatbox when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.chatbox = new Chatbox();
});

// Global functions for HTML onclick events
function handleFileSelect(input) {
    window.chatbox.handleFileSelect(input);
}

function clearFileSelection() {
    window.chatbox.clearFileSelection();
}

function testNotificationSound() {
    if (window.chatNotificationHandler) {
        window.chatNotificationHandler.testSound();
        window.chatNotificationHandler.testNotification();
    }
}
