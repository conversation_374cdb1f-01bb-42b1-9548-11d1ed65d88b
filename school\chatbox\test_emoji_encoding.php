<?php
header('Content-Type: text/html; charset=utf-8');
include '../db/dbconfig.php';

// Set UTF-8 encoding for database connection
mysqli_set_charset($connection, 'utf8mb4');

echo "<h2>Emoji Encoding Test</h2>";

// Test 1: Insert emoji into database
$test_emoji = "Hello 😀 World 🎉 Test 💖";
echo "<p><strong>Test Emoji String:</strong> " . $test_emoji . "</p>";

// Test database insertion
$stmt = $connection->prepare("INSERT INTO tbl_chat (sender_id, recipient_id, message, message_type, timestamp) VALUES (1, 2, ?, 'text', NOW())");
if ($stmt) {
    $stmt->bind_param("s", $test_emoji);
    if ($stmt->execute()) {
        $message_id = $connection->insert_id;
        echo "<p style='color: green;'>✅ Successfully inserted emoji message with ID: " . $message_id . "</p>";
        
        // Test retrieval
        $stmt2 = $connection->prepare("SELECT message FROM tbl_chat WHERE id = ?");
        $stmt2->bind_param("i", $message_id);
        $stmt2->execute();
        $result = $stmt2->get_result();
        $row = $result->fetch_assoc();
        
        if ($row) {
            echo "<p><strong>Retrieved from DB:</strong> " . $row['message'] . "</p>";
            
            if ($row['message'] === $test_emoji) {
                echo "<p style='color: green;'>✅ Emoji encoding/decoding works correctly!</p>";
            } else {
                echo "<p style='color: red;'>❌ Emoji encoding/decoding failed!</p>";
                echo "<p>Expected: " . $test_emoji . "</p>";
                echo "<p>Got: " . $row['message'] . "</p>";
            }
        }
        
        // Clean up test data
        $stmt3 = $connection->prepare("DELETE FROM tbl_chat WHERE id = ?");
        $stmt3->bind_param("i", $message_id);
        $stmt3->execute();
        echo "<p>Test data cleaned up.</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to insert emoji message: " . $stmt->error . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Failed to prepare statement: " . $connection->error . "</p>";
}

// Test 2: Check database charset
$result = $connection->query("SHOW TABLE STATUS LIKE 'tbl_chat'");
if ($result && $row = $result->fetch_assoc()) {
    echo "<h3>Database Table Info:</h3>";
    echo "<p><strong>Charset:</strong> " . ($row['Collation'] ?? 'Unknown') . "</p>";
    
    if (strpos($row['Collation'], 'utf8mb4') !== false) {
        echo "<p style='color: green;'>✅ Table uses utf8mb4 charset (supports emojis)</p>";
    } else {
        echo "<p style='color: red;'>❌ Table does not use utf8mb4 charset</p>";
        echo "<p>Run the fix_emoji_encoding.sql script to fix this.</p>";
    }
}

// Test 3: Check connection charset
$charset = $connection->character_set_name();
echo "<h3>Connection Info:</h3>";
echo "<p><strong>Connection Charset:</strong> " . $charset . "</p>";

if ($charset === 'utf8mb4') {
    echo "<p style='color: green;'>✅ Connection uses utf8mb4 charset</p>";
} else {
    echo "<p style='color: red;'>❌ Connection does not use utf8mb4 charset</p>";
}

// Test 4: JSON encoding test
$test_data = [
    'message' => $test_emoji,
    'sender' => 'Test User 👤',
    'timestamp' => date('Y-m-d H:i:s')
];

$json = json_encode($test_data, JSON_UNESCAPED_UNICODE);
echo "<h3>JSON Encoding Test:</h3>";
echo "<p><strong>JSON:</strong> " . $json . "</p>";

$decoded = json_decode($json, true);
if ($decoded && $decoded['message'] === $test_emoji) {
    echo "<p style='color: green;'>✅ JSON encoding/decoding works correctly!</p>";
} else {
    echo "<p style='color: red;'>❌ JSON encoding/decoding failed!</p>";
}

echo "<hr>";
echo "<h3>Common Emojis Test:</h3>";
$common_emojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '🎉', '🎊', '🎈', '🎁', '🎂', '🍰', '🎵', '🎶', '🎤', '🎧'
];

echo "<p>";
foreach ($common_emojis as $emoji) {
    echo $emoji . " ";
}
echo "</p>";

echo "<p>If you can see all the emojis above clearly, emoji support is working!</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

h2, h3 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}
</style>
