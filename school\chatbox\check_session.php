<?php
session_start();
header('Content-Type: application/json; charset=utf-8');

// Check session variables
$session_info = [
    'session_id' => session_id(),
    'session_status' => session_status(),
    'all_session_vars' => $_SESSION,
    'user_id_variants' => [
        'id' => $_SESSION['id'] ?? null,
        'user_id' => $_SESSION['user_id'] ?? null,
        'userid' => $_SESSION['userid'] ?? null,
        'ID' => $_SESSION['ID'] ?? null,
        'USER_ID' => $_SESSION['USER_ID'] ?? null
    ],
    'detected_user_id' => null
];

// Try to detect the correct user ID
if (isset($_SESSION['id'])) {
    $session_info['detected_user_id'] = $_SESSION['id'];
    $session_info['detected_from'] = 'id';
} elseif (isset($_SESSION['user_id'])) {
    $session_info['detected_user_id'] = $_SESSION['user_id'];
    $session_info['detected_from'] = 'user_id';
} elseif (isset($_SESSION['userid'])) {
    $session_info['detected_user_id'] = $_SESSION['userid'];
    $session_info['detected_from'] = 'userid';
} elseif (isset($_SESSION['ID'])) {
    $session_info['detected_user_id'] = $_SESSION['ID'];
    $session_info['detected_from'] = 'ID';
} elseif (isset($_SESSION['USER_ID'])) {
    $session_info['detected_user_id'] = $_SESSION['USER_ID'];
    $session_info['detected_from'] = 'USER_ID';
}

echo json_encode($session_info, JSON_PRETTY_PRINT);
?>
