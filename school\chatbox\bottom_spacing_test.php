<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Bottom Spacing Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .info-box {
            background: #17a2b8;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .spacing-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 200px;
        }
        
        .test-button {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            padding: 15px 30px;
            font-size: 16px;
        }
        
        .content-area {
            height: 120vh;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="spacing-info" id="spacing-info">
        <div><strong>📏 Spacing Info</strong></div>
        <div>Bottom Padding: 60px</div>
        <div>Bottom Margin: 40px</div>
        <div>Total Space: 100px</div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div>Input Status: <span id="input-status">Ready</span></div>
        </div>
    </div>
    
    <button class="btn btn-info btn-lg test-button" onclick="openChat()">
        <i class="fa fa-comments"></i> Test Bottom Spacing
    </button>
    
    <div class="info-box">
        <h2><i class="fa fa-arrows-alt-v"></i> Bottom Spacing Test</h2>
        <h4>Input Area Spacing Solution</h4>
        <p><strong>Approach:</strong> Add extra padding and margin to bottom of input area</p>
        <p><strong>Added:</strong> 60px bottom padding + 40px bottom margin = 100px total space</p>
    </div>
    
    <div class="content-area">
        <h2>How Bottom Spacing Works</h2>
        
        <div class="alert alert-info">
            <h5><i class="fa fa-info-circle"></i> The Solution</h5>
            <p>Instead of reducing chat height, we add extra space below the input field:</p>
            <ul>
                <li><strong>Bottom Padding:</strong> 60px (inside input area)</li>
                <li><strong>Bottom Margin:</strong> 40px (outside input area)</li>
                <li><strong>Total Buffer:</strong> 100px of extra space</li>
            </ul>
        </div>
        
        <h3>Expected Results:</h3>
        <div class="row">
            <div class="col-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">✅ Input Field Visibility</h6>
                        <p class="card-text">Input field should be pushed up by 100px, making it fully visible even when keyboard appears.</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">✅ Blue Border</h6>
                        <p class="card-text">Look for the blue border around the input area - it should be clearly visible.</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">✅ Extra Space</h6>
                        <p class="card-text">There should be noticeable empty space below the input field.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <h3>Test Steps:</h3>
        <ol>
            <li><strong>Click "Test Bottom Spacing"</strong> button</li>
            <li><strong>Look for blue border</strong> around input area</li>
            <li><strong>Notice extra space</strong> below input field</li>
            <li><strong>Try typing</strong> - input should stay visible</li>
            <li><strong>Check if keyboard covers input</strong> - it shouldn't!</li>
        </ol>
        
        <div class="alert alert-success">
            <h6><i class="fa fa-lightbulb"></i> Why This Works</h6>
            <p>By adding 100px of space below the input, we effectively "push" the input field higher up in the chat area. This creates a buffer zone that prevents the mobile keyboard from covering the input.</p>
        </div>
        
        <!-- Extra content for scrolling -->
        <div style="height: 300px; background: white; margin: 20px 0; padding: 20px; border-radius: 8px;">
            <h4>Sample Content</h4>
            <p>This is additional content to test the scrolling behavior and ensure the chat overlay works properly with the new bottom spacing.</p>
            <p>The bottom spacing approach is much simpler than complex positioning fixes and should work reliably across all mobile devices.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        function openChat() {
            console.log('🔧 Testing bottom spacing approach...');
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                
                // Check input area after chat opens
                setTimeout(() => {
                    checkInputSpacing();
                }, 1000);
            } else {
                alert('Chat toggle not found!');
            }
        }
        
        function checkInputSpacing() {
            const inputArea = document.querySelector('.chat-input-area, #message-input-area');
            const messageInput = document.getElementById('message-input');
            
            console.log('🔍 Checking input spacing...');
            
            if (inputArea) {
                const inputRect = inputArea.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(inputArea);
                
                const paddingBottom = computedStyle.paddingBottom;
                const marginBottom = computedStyle.marginBottom;
                
                console.log('Input area spacing:', {
                    paddingBottom: paddingBottom,
                    marginBottom: marginBottom,
                    bottom: inputRect.bottom,
                    screenHeight: window.innerHeight,
                    spaceBelow: window.innerHeight - inputRect.bottom
                });
                
                const spaceBelow = window.innerHeight - inputRect.bottom;
                const statusElement = document.getElementById('input-status');
                
                if (spaceBelow >= 50) {
                    statusElement.textContent = '✅ Good spacing';
                    statusElement.style.color = '#28a745';
                    console.log('✅ Good spacing detected');
                } else if (spaceBelow >= 0) {
                    statusElement.textContent = '⚠️ Tight spacing';
                    statusElement.style.color = '#ffc107';
                    console.log('⚠️ Tight spacing');
                } else {
                    statusElement.textContent = '❌ Overlapping';
                    statusElement.style.color = '#dc3545';
                    console.log('❌ Input overlapping viewport');
                }
                
                // Show detailed info
                alert(`📏 Spacing Analysis:
                
Bottom Padding: ${paddingBottom}
Bottom Margin: ${marginBottom}
Input Bottom: ${Math.round(inputRect.bottom)}px
Screen Height: ${window.innerHeight}px
Space Below: ${Math.round(spaceBelow)}px

${spaceBelow >= 50 ? '✅ Input should be fully visible!' : '❌ May need more spacing'}`);
                
            } else {
                console.log('❌ Input area not found');
                alert('❌ ERROR: Input area not found');
            }
            
            if (messageInput) {
                messageInput.focus();
                console.log('✅ Input field focused');
            }
        }
        
        console.log('📏 Bottom spacing test loaded');
        console.log('Approach: Add 60px padding + 40px margin = 100px total space');
    </script>
</body>
</html>
