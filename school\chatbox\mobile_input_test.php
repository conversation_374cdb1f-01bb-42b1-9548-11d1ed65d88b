<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Mobile Chat Input Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .test-header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .test-content {
            margin-top: 80px;
            padding: 20px;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }
        
        .debug-info {
            position: fixed;
            top: 80px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 200px;
        }
        
        .test-button {
            margin: 10px 0;
            width: 100%;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .status-good { background: #28a745; }
        .status-bad { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-header">
        <h4><i class="fa fa-mobile-alt"></i> Mobile Chat Input Test</h4>
        <small>Testing input visibility on mobile devices</small>
    </div>
    
    <div class="debug-info" id="debug-info">
        <div><strong>Debug Info:</strong></div>
        <div>Screen: <span id="screen-size"></span></div>
        <div>Viewport: <span id="viewport-size"></span></div>
        <div>Device: <span id="device-type"></span></div>
        <div>Input Status: <span id="input-status">Checking...</span></div>
    </div>
    
    <div class="test-content">
        <div class="alert alert-info">
            <h5><i class="fa fa-info-circle"></i> Instructions</h5>
            <ol>
                <li>Click the blue chat button (bottom right)</li>
                <li>Try to type in the message input</li>
                <li>The input should be visible above the keyboard</li>
                <li>Use the test buttons below if needed</li>
            </ol>
        </div>
        
        <button class="btn btn-primary test-button" onclick="openChat()">
            <i class="fa fa-comments"></i> Open Chat
        </button>
        
        <button class="btn btn-success test-button" onclick="forceInputVisible()">
            <i class="fa fa-eye"></i> Force Input Visible
        </button>
        
        <button class="btn btn-warning test-button" onclick="focusInput()">
            <i class="fa fa-keyboard"></i> Focus Input
        </button>
        
        <button class="btn btn-info test-button" onclick="checkInputStatus()">
            <i class="fa fa-search"></i> Check Input Status
        </button>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fa fa-list"></i> Test Results</h6>
            </div>
            <div class="card-body" id="test-results">
                <div id="input-found">
                    <span class="status-indicator status-warning"></span>
                    Input Element: Checking...
                </div>
                <div id="input-visible">
                    <span class="status-indicator status-warning"></span>
                    Input Visible: Checking...
                </div>
                <div id="input-position">
                    <span class="status-indicator status-warning"></span>
                    Input Position: Checking...
                </div>
                <div id="chat-open">
                    <span class="status-indicator status-warning"></span>
                    Chat Open: Checking...
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fa fa-code"></i> Console Logs</h6>
            </div>
            <div class="card-body">
                <div id="console-logs" style="font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                    Console logs will appear here...
                </div>
            </div>
        </div>
        
        <!-- Spacer to ensure scrolling -->
        <div style="height: 200px;"></div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="force_mobile_input.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const logContainer = document.getElementById('console-logs');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logEntry = document.createElement('div');
            logEntry.textContent = new Date().toLocaleTimeString() + ': ' + args.join(' ');
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        };
        
        // Update debug info
        function updateDebugInfo() {
            document.getElementById('screen-size').textContent = `${screen.width}x${screen.height}`;
            document.getElementById('viewport-size').textContent = `${window.innerWidth}x${window.innerHeight}`;
            
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            document.getElementById('device-type').textContent = isMobile ? 'Mobile' : 'Desktop';
        }
        
        // Test functions
        function openChat() {
            console.log('🔧 Opening chat...');
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                setTimeout(checkInputStatus, 1000);
            } else {
                console.log('❌ Chat toggle not found');
            }
        }
        
        function forceInputVisible() {
            console.log('🔧 Forcing input visible...');
            if (window.forceMobileInputVisible) {
                window.forceMobileInputVisible();
            } else {
                console.log('❌ Force function not available');
            }
            setTimeout(checkInputStatus, 500);
        }
        
        function focusInput() {
            console.log('🔧 Focusing input...');
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.focus();
                messageInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                console.log('✅ Input focused');
            } else {
                console.log('❌ Message input not found');
            }
            setTimeout(checkInputStatus, 500);
        }
        
        function checkInputStatus() {
            console.log('🔧 Checking input status...');
            
            // Check if input element exists
            const messageInput = document.getElementById('message-input');
            const inputArea = document.querySelector('.chat-input-area, #message-input-area');
            const chatPanel = document.getElementById('chat-panel');
            
            // Update status indicators
            updateStatus('input-found', messageInput !== null, 'Input Element');
            updateStatus('chat-open', chatPanel && chatPanel.classList.contains('open'), 'Chat Open');
            
            if (inputArea) {
                const rect = inputArea.getBoundingClientRect();
                const isVisible = rect.bottom <= window.innerHeight && rect.top >= 0;
                const position = `Top: ${Math.round(rect.top)}, Bottom: ${Math.round(rect.bottom)}, Height: ${Math.round(rect.height)}`;
                
                updateStatus('input-visible', isVisible, 'Input Visible');
                updateStatus('input-position', true, `Position: ${position}`);
                
                console.log(`📊 Input area position: ${position}`);
                console.log(`📊 Window height: ${window.innerHeight}`);
                console.log(`📊 Input visible: ${isVisible}`);
            } else {
                updateStatus('input-visible', false, 'Input Visible');
                updateStatus('input-position', false, 'Input Position');
                console.log('❌ Input area not found');
            }
        }
        
        function updateStatus(elementId, isGood, label) {
            const element = document.getElementById(elementId);
            const indicator = element.querySelector('.status-indicator');
            
            if (isGood) {
                indicator.className = 'status-indicator status-good';
                element.innerHTML = `<span class="status-indicator status-good"></span>${label}: ✅ Good`;
            } else {
                indicator.className = 'status-indicator status-bad';
                element.innerHTML = `<span class="status-indicator status-bad"></span>${label}: ❌ Problem`;
            }
        }
        
        // Initialize
        updateDebugInfo();
        window.addEventListener('resize', updateDebugInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDebugInfo, 500);
        });
        
        // Auto-check status every 3 seconds
        setInterval(checkInputStatus, 3000);
        
        console.log('🔧 Mobile input test page loaded');
        console.log('📱 User agent: ' + navigator.userAgent);
    </script>
</body>
</html>
