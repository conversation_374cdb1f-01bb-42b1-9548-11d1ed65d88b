<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Chat Toggle Position Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
            height: 100vh;
        }
        
        .info-box {
            background: #28a745;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .position-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 250px;
        }
        
        .test-area {
            height: 150vh;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 8px;
        }
        
        .send-button-demo {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 20px;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .position-marker {
            position: fixed;
            right: 10px;
            width: 2px;
            background: #ffc107;
            z-index: 10001;
        }
        
        .marker-120 {
            bottom: 120px;
            height: 60px;
        }
        
        .marker-20 {
            bottom: 20px;
            height: 60px;
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="position-info" id="position-info">
        <div><strong>📍 Position Info</strong></div>
        <div>Chat Toggle: <span id="toggle-position">Checking...</span></div>
        <div>Send Button: <span id="send-position">bottom: 20px</span></div>
        <div>Separation: <span id="separation">100px</span></div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div>Status: <span id="overlap-status">Testing...</span></div>
        </div>
    </div>
    
    <!-- Position markers for visual reference -->
    <div class="position-marker marker-120" title="Chat Toggle Position (120px from bottom)"></div>
    <div class="position-marker marker-20" title="Send Button Position (20px from bottom)"></div>
    
    <!-- Demo send button to show potential conflict -->
    <button class="send-button-demo" title="Demo Send Button Position">
        <i class="fa fa-paper-plane"></i>
    </button>
    
    <div class="info-box">
        <h2><i class="fa fa-crosshairs"></i> Chat Toggle Position Test</h2>
        <h4>Fixing Icon Overlap Issue</h4>
        <p><strong>Problem:</strong> Chat toggle covering send button</p>
        <p><strong>Solution:</strong> Moved chat toggle from bottom: 20px to bottom: 120px</p>
    </div>
    
    <div class="test-area">
        <h2>Position Fix Details</h2>
        
        <div class="alert alert-warning">
            <h5><i class="fa fa-exclamation-triangle"></i> The Problem</h5>
            <p>The floating chat toggle button was positioned at <code>bottom: 20px</code>, which could overlap with the send button inside the chat (also typically positioned near the bottom).</p>
        </div>
        
        <div class="alert alert-success">
            <h5><i class="fa fa-check-circle"></i> The Solution</h5>
            <p>Moved the chat toggle button to <code>bottom: 120px</code>, creating 100px separation from the typical send button position.</p>
        </div>
        
        <h3>Visual References:</h3>
        <div class="row">
            <div class="col-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🟡 Yellow Line (Right Edge)</h6>
                        <p class="card-text">Shows chat toggle position at 120px from bottom</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🔴 Red Line (Right Edge)</h6>
                        <p class="card-text">Shows typical send button position at 20px from bottom</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🔴 Red Button (Bottom Right)</h6>
                        <p class="card-text">Demo send button to show where overlap would occur</p>
                    </div>
                </div>
            </div>
        </div>
        
        <h3>Test Steps:</h3>
        <ol>
            <li><strong>Look at the right edge</strong> - see yellow and red position markers</li>
            <li><strong>Notice the separation</strong> - 100px gap between positions</li>
            <li><strong>Click the chat toggle</strong> - should be at yellow line position</li>
            <li><strong>Open chat and check send button</strong> - should not overlap</li>
            <li><strong>Verify chat toggle hides</strong> when chat is open</li>
        </ol>
        
        <div class="alert alert-info">
            <h6><i class="fa fa-lightbulb"></i> Additional Fix</h6>
            <p>The chat toggle button now automatically hides when the chat is open, preventing any potential interference with chat functionality.</p>
        </div>
        
        <h3>Position Calculations:</h3>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Element</th>
                    <th>Position</th>
                    <th>Height</th>
                    <th>Range</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Send Button</td>
                    <td>bottom: 20px</td>
                    <td>~60px</td>
                    <td>20px - 80px</td>
                </tr>
                <tr>
                    <td>Chat Toggle (Old)</td>
                    <td>bottom: 20px</td>
                    <td>~60px</td>
                    <td>20px - 80px</td>
                </tr>
                <tr class="table-success">
                    <td>Chat Toggle (New)</td>
                    <td>bottom: 120px</td>
                    <td>~60px</td>
                    <td>120px - 180px</td>
                </tr>
            </tbody>
        </table>
        
        <!-- Extra content for scrolling -->
        <div style="height: 300px; background: white; margin: 20px 0; padding: 20px; border-radius: 8px;">
            <h4>Sample Content</h4>
            <p>This content ensures the page is scrollable and tests the fixed positioning behavior of both the chat toggle and the demo send button.</p>
            <p>The 100px separation should be sufficient to prevent any overlap between the chat toggle and send buttons on all mobile devices.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        function checkPositions() {
            const chatToggle = document.getElementById('chat-toggle');
            const demoSendButton = document.querySelector('.send-button-demo');
            
            if (chatToggle) {
                const toggleRect = chatToggle.getBoundingClientRect();
                const toggleBottom = window.innerHeight - toggleRect.bottom;
                
                document.getElementById('toggle-position').textContent = `bottom: ${Math.round(toggleBottom)}px`;
                
                if (demoSendButton) {
                    const sendRect = demoSendButton.getBoundingClientRect();
                    const sendBottom = window.innerHeight - sendRect.bottom;
                    const separation = Math.abs(toggleRect.bottom - sendRect.bottom);
                    
                    document.getElementById('separation').textContent = `${Math.round(separation)}px`;
                    
                    const statusElement = document.getElementById('overlap-status');
                    if (separation >= 80) {
                        statusElement.textContent = '✅ No overlap';
                        statusElement.style.color = '#28a745';
                    } else if (separation >= 40) {
                        statusElement.textContent = '⚠️ Close';
                        statusElement.style.color = '#ffc107';
                    } else {
                        statusElement.textContent = '❌ Overlapping';
                        statusElement.style.color = '#dc3545';
                    }
                }
            }
        }
        
        // Check positions on load and resize
        window.addEventListener('load', checkPositions);
        window.addEventListener('resize', checkPositions);
        
        // Monitor chat toggle visibility
        const chatToggle = document.getElementById('chat-toggle');
        const chatPanel = document.getElementById('chat-panel');
        
        if (chatToggle && chatPanel) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const isOpen = chatPanel.classList.contains('open');
                        console.log('Chat panel open:', isOpen);
                        console.log('Chat toggle visible:', window.getComputedStyle(chatToggle).display !== 'none');
                    }
                });
            });
            
            observer.observe(chatPanel, {
                attributes: true,
                attributeFilter: ['class']
            });
        }
        
        console.log('📍 Position test loaded');
        console.log('Chat toggle moved from bottom: 20px to bottom: 120px');
        console.log('Expected separation: 100px');
    </script>
</body>
</html>
