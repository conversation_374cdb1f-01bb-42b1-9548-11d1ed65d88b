<?php
// Chatbox Integration Script
// Include this file in all pages within the /school/ directory

// Only show chatbox if user is logged in
if (isset($_SESSION['id'])) {
?>
    <!-- Chatbox Integration -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Include Chatbox -->
    <?php include 'chatbox/chatbox.php'; ?>
    
    <!-- Chatbox JavaScript -->
    <script src="chatbox/notification_handler.js"></script>
    <script src="chatbox/emoticons.js"></script>
    <script src="chatbox/chatbox.js"></script>
    
    <script>
        // Set current user ID for JavaScript
        window.currentUserId = <?php echo $_SESSION['id']; ?>;
        
        // Initialize user status on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Update user status to online when page loads
            if (window.chatbox) {
                window.chatbox.updateUserStatus('online');
            }
        });
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (window.chatbox) {
                if (document.hidden) {
                    window.chatbox.updateUserStatus('away');
                } else {
                    window.chatbox.updateUserStatus('online');
                }
            }
        });
        
        // Handle page unload
        window.addEventListener('beforeunload', function() {
            if (window.chatbox) {
                window.chatbox.updateUserStatus('offline');
            }
        });
        
        // Auto-refresh conversations every 30 seconds
        setInterval(function() {
            if (window.chatbox && document.getElementById('chat-panel').classList.contains('open')) {
                window.chatbox.loadConversations();
            }
        }, 30000);
    </script>
    
    <style>
        /* Additional responsive styles */
        @media (max-width: 768px) {
            .chat-panel {
                width: 100vw;
                right: -100vw;
            }
            
            .chat-window {
                width: 100vw;
                right: -100vw;
            }
            
            .chat-toggle {
                bottom: 80px;
                right: 20px;
            }
        }
        
        /* Ensure chatbox appears above other elements */
        #chatbox-container {
            z-index: 999999 !important;
        }
        
        .chat-panel,
        .chat-window {
            z-index: 999999 !important;
        }
        
        .chat-toggle {
            z-index: 1000000 !important;
        }
        
        /* Custom scrollbar for better UX */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }
        
        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* Animation for new messages */
        @keyframes slideInMessage {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message {
            animation: slideInMessage 0.3s ease-out;
        }
        
        /* Pulse animation for notification badge */
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }
        
        .chat-badge {
            animation: pulse 2s infinite;
        }
        
        /* Typing indicator animation */
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
        
        .typing-indicator i {
            animation: typing 1.4s infinite;
        }
        
        /* Status indicator glow effect */
        .status-online {
            box-shadow: 0 0 6px #28a745;
        }
        
        .status-away {
            box-shadow: 0 0 6px #ffc107;
        }
        
        .status-busy {
            box-shadow: 0 0 6px #dc3545;
        }
        
        /* Hover effects */
        .chat-list-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
            transition: all 0.2s ease;
        }
        
        .chat-toggle:hover {
            transform: translateY(-50%) scale(1.05);
            box-shadow: -3px 0 15px rgba(0,0,0,0.2);
        }
        
        /* Message bubble improvements */
        .message-content {
            word-wrap: break-word;
            max-width: 280px;
        }
        
        .message.own .message-content {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }
        
        /* File message styling */
        .message-file {
            transition: background-color 0.2s ease;
        }
        
        .message-file:hover {
            background-color: #e9ecef;
        }
        
        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
<?php
}
?>
