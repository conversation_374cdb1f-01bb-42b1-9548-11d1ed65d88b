<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Real-time Message Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .info-box {
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.95);
            color: white;
            padding: 20px;
            border-radius: 8px;
            font-size: 11px;
            z-index: 10000;
            max-width: 350px;
            max-height: 80vh;
            overflow-y: auto;
            border: 2px solid #e74c3c;
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e74c3c;
        }
        
        .test-controls button {
            margin: 3px 0;
            display: block;
            width: 200px;
            font-size: 12px;
        }
        
        .content-area {
            height: 120vh;
            background: linear-gradient(to bottom, #ffe6e6, #ffcccc);
            padding: 20px;
            border-radius: 8px;
        }
        
        .message-counter {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #e74c3c;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <div class="debug-panel" id="debug-panel">
        <div><strong>🐛 Real-time Debug</strong></div>
        <div>Messages in DOM: <span id="dom-count">0</span></div>
        <div>Last Message ID: <span id="last-id">0</span></div>
        <div>Current Recipient: <span id="current-recipient">None</span></div>
        <div>Polling Active: <span id="polling-status">Unknown</span></div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div><strong>Recent Actions:</strong></div>
            <div id="action-log" style="max-height: 200px; overflow-y: auto; font-size: 10px;"></div>
        </div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <button class="btn btn-sm btn-warning" onclick="clearLog()">Clear Log</button>
        </div>
    </div>
    
    <div class="test-controls">
        <div><strong>🧪 Message Tests</strong></div>
        <button class="btn btn-primary btn-sm" onclick="openChat()">
            <i class="fa fa-comments"></i> Open Chat
        </button>
        <button class="btn btn-warning btn-sm" onclick="selectRecipient()">
            <i class="fa fa-user"></i> Select Recipient
        </button>
        <button class="btn btn-success btn-sm" onclick="sendTestMessage()">
            <i class="fa fa-paper-plane"></i> Send Test Message
        </button>
        <button class="btn btn-info btn-sm" onclick="checkMessages()">
            <i class="fa fa-search"></i> Check Messages
        </button>
        <button class="btn btn-warning btn-sm" onclick="forceRefresh()">
            <i class="fa fa-sync"></i> Force Refresh
        </button>
        <button class="btn btn-secondary btn-sm" onclick="simulateReceive()">
            <i class="fa fa-download"></i> Simulate Receive
        </button>
        <button class="btn btn-danger btn-sm" onclick="debugInfo()">
            <i class="fa fa-bug"></i> Full Debug
        </button>
    </div>
    
    <div class="message-counter" id="message-counter">
        Messages: <span id="message-count">0</span>
    </div>
    
    <div class="info-box">
        <h2><i class="fa fa-exclamation-triangle"></i> Real-time Message Issue</h2>
        <h4>Messages Not Appearing Dynamically</h4>
        <p><strong>Problem:</strong> Sent messages don't appear until chat is closed/reopened</p>
        <p><strong>Testing:</strong> Send message debugging and real-time updates</p>
    </div>
    
    <div class="content-area">
        <h2>Real-time Message Debugging</h2>
        
        <div class="alert alert-danger">
            <h5><i class="fa fa-exclamation-triangle"></i> The Issue</h5>
            <p>Messages are being sent successfully but not appearing in real-time in the chat interface. Both sender and recipient need to close/reopen chat to see new messages.</p>
        </div>
        
        <div class="alert alert-info">
            <h5><i class="fa fa-tools"></i> Debug Features</h5>
            <ul>
                <li><strong>Real-time DOM monitoring:</strong> Counts messages in chat container</li>
                <li><strong>Action logging:</strong> Tracks all message-related actions</li>
                <li><strong>Function testing:</strong> Test send/receive functions individually</li>
                <li><strong>Console debugging:</strong> Enhanced logging for troubleshooting</li>
            </ul>
        </div>
        
        <h3>Test Scenarios:</h3>
        <div class="row">
            <div class="col-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🔵 Test 1: Send Message</h6>
                        <p class="card-text">Click "Send Test Message" → Auto-selects recipient and sends message</p>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🟣 Test 1b: Select Recipient</h6>
                        <p class="card-text">Click "Select Recipient" → Manually select a user to chat with</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🟢 Test 2: Check DOM</h6>
                        <p class="card-text">Click "Check Messages" → Shows actual DOM content</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🟡 Test 3: Force Refresh</h6>
                        <p class="card-text">Click "Force Refresh" → Reloads messages from server</p>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">🟠 Test 4: Simulate Receive</h6>
                        <p class="card-text">Click "Simulate Receive" → Tests recipient side</p>
                    </div>
                </div>
            </div>
        </div>
        
        <h3>Expected Behavior:</h3>
        <ul>
            <li><strong>Sender:</strong> Message appears immediately after sending</li>
            <li><strong>Recipient:</strong> Message appears within 2-3 seconds (polling interval)</li>
            <li><strong>DOM Count:</strong> Should increase when messages are added</li>
            <li><strong>No Refresh:</strong> Should work without closing/reopening chat</li>
        </ul>
        
        <div class="alert alert-warning">
            <h6><i class="fa fa-lightbulb"></i> Debugging Tips</h6>
            <p>Watch the debug panel (top-right) while testing. The action log will show exactly what functions are being called and whether messages are being added to the DOM.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        let actionLogCount = 0;
        
        function logAction(action, details = '') {
            actionLogCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `${actionLogCount}. [${timestamp}] ${action} ${details}`;
            
            const logContainer = document.getElementById('action-log');
            const logDiv = document.createElement('div');
            logDiv.textContent = logEntry;
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log('📝', logEntry);
        }
        
        function updateDebugInfo() {
            const container = document.getElementById('chat-messages');
            const messageCount = container ? container.children.length : 0;
            
            document.getElementById('dom-count').textContent = messageCount;
            document.getElementById('message-count').textContent = messageCount;
            
            if (window.chatbox) {
                document.getElementById('last-id').textContent = window.chatbox.lastMessageId || 0;
                document.getElementById('current-recipient').textContent = window.chatbox.currentRecipientId || 'None';
                document.getElementById('polling-status').textContent = window.chatbox.pollingInterval ? 'Active' : 'Inactive';
            }
        }
        
        function openChat() {
            logAction('Opening chat...');
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                setTimeout(() => {
                    logAction('Chat opened, checking state...');
                    updateDebugInfo();
                }, 1000);
            } else {
                logAction('ERROR: Chat toggle not found');
            }
        }

        async function selectRecipient() {
            logAction('Selecting recipient...');

            try {
                // First, make sure chat is open
                if (!document.getElementById('chat-panel')?.classList.contains('open')) {
                    openChat();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // Get users from API
                const response = await fetch('chat_api.php?action=get_users');
                const data = await response.json();

                if (data.success && data.users && data.users.length > 0) {
                    const firstUser = data.users[0];
                    logAction(`Found ${data.users.length} users, selecting: ${firstUser.name} (ID: ${firstUser.id})`);

                    // Set recipient in chatbox
                    if (window.chatbox) {
                        window.chatbox.currentRecipientId = firstUser.id;
                        window.chatbox.currentRecipientName = firstUser.name;
                        logAction(`Recipient set: ${firstUser.name} (${firstUser.id})`);

                        // Load messages for this recipient
                        if (window.chatbox.loadMessages) {
                            window.chatbox.loadMessages(firstUser.id);
                            logAction('Loading messages for recipient...');
                        }

                        // Update UI to show selected recipient
                        const chatHeader = document.querySelector('.chat-header h5, .chat-title');
                        if (chatHeader) {
                            chatHeader.textContent = `Chat with ${firstUser.name}`;
                        }

                        updateDebugInfo();
                        logAction('✅ Recipient selected successfully');
                    } else {
                        logAction('ERROR: Chatbox object not found');
                    }
                } else {
                    logAction('ERROR: No users found or API error');
                    console.error('Users API response:', data);
                }
            } catch (error) {
                logAction(`ERROR: Failed to select recipient - ${error.message}`);
                console.error('Select recipient error:', error);
            }
        }
        
        async function sendTestMessage() {
            logAction('Sending test message...');

            // Auto-select recipient if none selected
            if (!window.chatbox || !window.chatbox.currentRecipientId) {
                logAction('No recipient selected, auto-selecting...');
                await selectRecipient();

                // Check again after auto-selection
                if (!window.chatbox || !window.chatbox.currentRecipientId) {
                    logAction('ERROR: Failed to auto-select recipient');
                    alert('Failed to select a recipient. Please try "Select Recipient" first.');
                    return;
                }
            }
            
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                const testMessage = `Test message ${Date.now()}`;
                messageInput.value = testMessage;
                logAction('Test message set in input:', testMessage);
                
                // Trigger send
                const sendButton = document.querySelector('#send-message, .send-btn, [onclick*="send"]');
                if (sendButton) {
                    sendButton.click();
                    logAction('Send button clicked');
                } else {
                    // Try Enter key
                    const event = new KeyboardEvent('keypress', { key: 'Enter' });
                    messageInput.dispatchEvent(event);
                    logAction('Enter key simulated');
                }
                
                setTimeout(() => {
                    updateDebugInfo();
                    logAction('Message count after send:', document.getElementById('chat-messages')?.children.length || 0);
                }, 1000);
            } else {
                logAction('ERROR: Message input not found');
            }
        }
        
        function checkMessages() {
            logAction('Checking messages in DOM...');
            const container = document.getElementById('chat-messages');
            
            if (container) {
                const messages = container.querySelectorAll('[data-message-id]');
                logAction(`Found ${messages.length} messages in DOM`);
                
                messages.forEach((msg, index) => {
                    const id = msg.getAttribute('data-message-id');
                    const text = msg.textContent.substring(0, 30) + '...';
                    logAction(`  ${index + 1}. ID:${id} - ${text}`);
                });
                
                updateDebugInfo();
            } else {
                logAction('ERROR: Chat messages container not found');
            }
        }
        
        function forceRefresh() {
            logAction('Force refreshing messages...');
            if (window.chatbox && window.chatbox.loadMessages) {
                window.chatbox.loadMessages(window.chatbox.currentRecipientId);
                logAction('loadMessages called');
                setTimeout(updateDebugInfo, 1000);
            } else {
                logAction('ERROR: loadMessages function not available');
            }
        }
        
        function simulateReceive() {
            logAction('Simulating message receive...');
            
            const testMessage = {
                id: Date.now(),
                sender_id: 999,
                recipient_id: window.currentUserId || 1,
                message: 'Simulated received message',
                message_type: 'text',
                timestamp: new Date().toISOString(),
                sender_name: 'Test Sender',
                sender_image: 'profile.png'
            };
            
            if (window.chatbox && window.chatbox.appendNewMessage) {
                window.chatbox.appendNewMessage(testMessage);
                logAction('appendNewMessage called with simulated message');
                setTimeout(updateDebugInfo, 500);
            } else {
                logAction('ERROR: appendNewMessage function not available');
            }
        }
        
        function debugInfo() {
            logAction('Generating full debug info...');
            
            const info = {
                chatboxExists: !!window.chatbox,
                currentRecipientId: window.chatbox?.currentRecipientId,
                lastMessageId: window.chatbox?.lastMessageId,
                pollingActive: !!window.chatbox?.pollingInterval,
                containerExists: !!document.getElementById('chat-messages'),
                messageCount: document.getElementById('chat-messages')?.children.length || 0,
                inputExists: !!document.getElementById('message-input'),
                sendButtonExists: !!document.querySelector('#send-message, .send-btn')
            };
            
            console.log('🐛 Full Debug Info:', info);
            logAction('Full debug info logged to console');
            
            alert(`Debug Info:
            
Chatbox Object: ${info.chatboxExists ? 'Found' : 'Missing'}
Current Recipient: ${info.currentRecipientId || 'None'}
Last Message ID: ${info.lastMessageId || 0}
Polling Active: ${info.pollingActive ? 'Yes' : 'No'}
Messages Container: ${info.containerExists ? 'Found' : 'Missing'}
Message Count: ${info.messageCount}
Input Field: ${info.inputExists ? 'Found' : 'Missing'}
Send Button: ${info.sendButtonExists ? 'Found' : 'Missing'}

Check console for detailed info.`);
        }
        
        function clearLog() {
            document.getElementById('action-log').innerHTML = '';
            actionLogCount = 0;
            logAction('Log cleared');
        }
        
        // Auto-update debug info every 2 seconds
        setInterval(updateDebugInfo, 2000);
        
        // Initial setup
        setTimeout(() => {
            logAction('Real-time message test loaded');
            updateDebugInfo();
        }, 1000);
        
        console.log('🧪 Real-time message test page loaded');
        console.log('Use the test controls to debug message sending/receiving');
    </script>
</body>
</html>
